import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import warnings
import requests
import ta
from datetime import datetime, timedelta

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置 ---
MODEL_DIR = "btc_reflection_model_v1"
FILE_PATH = "BTCUSDT_30m_2020-01-01_to_2024-12-31_minimal_features.csv" # 历史数据文件，用于验证
PREDICTION_WINDOW = 1
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
REFLECTION_DATA_PATH = os.path.join(MODEL_DIR, "reflection_data.csv")
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"

# 运行模式配置
REAL_TIME_MODE = True  # True: 真实环境模式，False: 模拟模式

# --- 核心函数 ---

def fetch_latest_klines_from_binance(symbol='BTCUSDT', interval='30m', limit=150):
    """
    从币安API获取最新的K线数据
    """
    try:
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        print(f"正在从币安API获取 {symbol} {interval} K线数据...")
        response = requests.get(BINANCE_API_URL, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        # 转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # 数据类型转换
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'number_of_trades']:
            df[col] = df[col].astype(float)

        # 重命名和选择需要的列
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'number_of_trades']].copy()
        df.rename(columns={'timestamp': 'timestamp_utc'}, inplace=True)
        df.set_index('timestamp_utc', inplace=True)

        print(f"成功获取 {len(df)} 条K线数据，时间范围: {df.index[0]} 到 {df.index[-1]}")
        return df

    except Exception as e:
        print(f"获取币安API数据失败: {e}")
        return None

def create_features(df):
    """
    从原始K线数据创建丰富的特征 (与训练数据完全一致)
    包含所有训练时使用的技术指标
    """
    df_feat = df.copy()

    try:
        # 使用 ta 库计算技术指标，与训练数据保持一致
        # 动量指标
        df_feat['momentum_rsi'] = ta.momentum.RSIIndicator(df_feat['close']).rsi()

        # 趋势指标
        macd = ta.trend.MACD(df_feat['close'])
        df_feat['trend_macd'] = macd.macd()
        df_feat['trend_macd_signal'] = macd.macd_signal()
        df_feat['trend_macd_diff'] = macd.macd_diff()

        # 波动率指标 (布林带)
        bb = ta.volatility.BollingerBands(df_feat['close'])
        df_feat['volatility_bbm'] = bb.bollinger_mavg()
        df_feat['volatility_bbh'] = bb.bollinger_hband()
        df_feat['volatility_bbl'] = bb.bollinger_lband()
        df_feat['volatility_bbw'] = bb.bollinger_wband()
        df_feat['volatility_bbp'] = bb.bollinger_pband()

        # ADX 趋势强度指标
        adx = ta.trend.ADXIndicator(df_feat['high'], df_feat['low'], df_feat['close'])
        df_feat['trend_adx'] = adx.adx()
        df_feat['trend_adx_pos'] = adx.adx_pos()
        df_feat['trend_adx_neg'] = adx.adx_neg()

        # 成交量指标
        df_feat['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(df_feat['close'], df_feat['volume']).on_balance_volume()

        # 基础价格特征
        df_feat['price_change'] = df_feat['close'].pct_change()
        df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
        df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

        # 多时间框架特征
        windows = [3, 5, 10, 20, 50, 100]
        for window in windows:
            df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
            df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
            df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
            df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

        # 额外的技术指标（与训练脚本一致）
        delta = df_feat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-9)
        df_feat['rsi_14'] = 100 - (100 / (1 + rs))

        ema_fast = df_feat['close'].ewm(span=12).mean()
        ema_slow = df_feat['close'].ewm(span=26).mean()
        df_feat['macd'] = ema_fast - ema_slow
        df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
        df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

        df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
        df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
        df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

        # 清理数据
        df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
        df_feat.dropna(inplace=True)

        return df_feat

    except Exception as e:
        print(f"特征工程过程出错: {e}")
        return pd.DataFrame()

def prepare_data_for_reflection(file_path, prediction_window):
    """
    【优化点】一个轻量级的数据准备函数，仅用于获取验证所需的'actual_target'，避免重复计算所有特征。
    """
    print("\n--- (验证) 高效加载历史数据以获取真实目标 ---")
    df = pd.read_csv(file_path)
    df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
    df.set_index('timestamp_utc', inplace=True)
    
    # 计算未来回报率
    df['future_return'] = df['close'].pct_change(periods=-prediction_window).shift(-prediction_window)
    
    # 使用与训练时相同的逻辑计算动态阈值
    threshold = df['future_return'].rolling(window=2000, min_periods=500).std().mean() * 0.5
    print(f"使用动态阈值: ±{threshold:.6f} 来验证预测")
    
    # 定义真实目标
    df['actual_target'] = 0
    df.loc[df['future_return'] > threshold, 'actual_target'] = 1
    
    # 只返回包含真实目标的Series，以时间为索引
    return df['actual_target']

# --- 加载模型和工具 (程序启动时执行一次) ---
print("--- 加载已训练的模型和预处理器 ---")
try:
    model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
    scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
    feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))
except FileNotFoundError:
    print(f"错误：在目录 '{MODEL_DIR}' 中找不到初始模型。请先成功运行 'train_initial_model.py'。")
    exit()

def predict_new_data(new_ohlcv_df):
    """
    对新的K线数据进行预测并记录。
    【修正点】此函数现在能处理原始OHLCV数据，因为它内部调用了create_features。
    """
    print("\n--- 1. 对新数据进行预测 ---")
    
    # 1. 从原始OHLCV数据创建特征
    df_new_featured = create_features(new_ohlcv_df)
    
    if df_new_featured.empty:
        print("特征工程后没有可供预测的新数据 (可能是因为窗口期数据不足)。")
        return

    # 确保所有需要的特征列都存在
    if not all(col in df_new_featured.columns for col in feature_columns):
        print("错误：新数据创建的特征与模型训练时的特征不匹配。")
        return

    # 2. 准备预测数据
    X_new = df_new_featured[feature_columns]
    X_new_scaled = scaler.transform(X_new)
    
    # 3. 预测
    predictions = model.predict(X_new_scaled)
    probabilities = model.predict_proba(X_new_scaled)[:, 1] # 获取上涨的概率

    # 4. 记录预测结果
    log_df = X_new.copy()
    log_df['prediction'] = predictions
    log_df['prediction_proba_up'] = probabilities
    
    # 【修正点】保存日志时要包含索引(时间戳)，以便后续匹配
    if os.path.exists(PREDICTION_LOG_PATH):
        log_df.to_csv(PREDICTION_LOG_PATH, mode='a', header=False, index=True)
    else:
        log_df.to_csv(PREDICTION_LOG_PATH, index=True)
        
    print(f"成功预测并记录了 {len(log_df)} 条新数据。")

def retrain_with_reflection(historical_data_path, prediction_window):
    """
    【已修正】核心的"反思"与"学习"环节。
    """
    global model  # 声明我们要修改全局的model变量
    print("\n--- 2. 开始反思与重训练循环 ---")

    # 1. 加载预测日志
    if not os.path.exists(PREDICTION_LOG_PATH):
        print("没有预测日志，无法进行反思学习。")
        return
        
    log_df = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True)
    log_df = log_df[~log_df.index.duplicated(keep='last')] # 保险起见，去重

    # 2. 【优化点】高效验证预测结果 (获取真实标签)
    actual_targets = prepare_data_for_reflection(historical_data_path, prediction_window)
    
    # 合并预测和真实结果
    reflection_df = log_df.join(actual_targets, how='inner')
    reflection_df.dropna(subset=['actual_target'], inplace=True)
    reflection_df['actual_target'] = reflection_df['actual_target'].astype(int)
    
    if reflection_df.empty:
        print("无法将预测日志与实际结果对齐，请检查时间戳。跳过重训练。")
        os.remove(PREDICTION_LOG_PATH) # 清理旧日志
        return

    reflection_df['is_correct'] = (reflection_df['prediction'] == reflection_df['actual_target']).astype(int)

    print(f"验证了 {len(reflection_df)} 条预测。")
    print(f"其中，正确率: {reflection_df['is_correct'].mean():.2%}")

    # 3. "反思" - 识别错误样本
    error_samples = reflection_df[reflection_df['is_correct'] == 0]
    print(f"识别出 {len(error_samples)} 个错误样本，这些是宝贵的学习资料。")
    
    if len(error_samples) < 10: # 如果错误样本太少，也跳过训练，避免过拟合
        print("错误样本过少，本次不进行重训练以保持模型稳定。")
        # 清理日志，为下个周期做准备
        os.remove(PREDICTION_LOG_PATH)
        return

    # 4. "学习" - 准备重训练数据
    X_new_verified = reflection_df[feature_columns]
    y_new_verified = reflection_df['actual_target']
    
    # 关键步骤：定义样本权重。让模型更关注它犯错的地方。
    sample_weights = reflection_df['is_correct'].apply(lambda x: 1 if x == 1 else 3).values
    
    X_new_scaled = scaler.transform(X_new_verified)
    
    # 创建 LightGBM 数据集
    train_data = lgb.Dataset(X_new_scaled, label=y_new_verified, weight=sample_weights)
    
    print("\n--- 3. 使用反思数据进行增量训练 ---")
    
    # 【核心修正点】使用正确的 lgb.train API
    params = model.get_params()
    params['metric'] = 'binary_logloss' # API 要求在 params 中定义 metric
    params.pop('n_estimators', None)
    params.pop('importance_type', None)
    
    # 使用 `lgb.train` 进行增量学习
    new_booster = lgb.train(
        params=params,
        train_set=train_data,
        init_model=model,                  # 从旧模型(LGBMClassifier实例)继续训练
        num_boost_round=200,               # 再训练最多200轮
        valid_sets=[train_data],           # 【修正】使用 valid_sets
        callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)] # 【修正】正确的早停回调
    )

    # 5. 保存更新后的模型
    # 【修正】采用更简单可靠的方法：先保存新booster，然后重新训练一个完整的模型
    
    # 先保存新的booster到临时文件
    temp_booster_path = os.path.join(MODEL_DIR, "temp_booster.txt")
    new_booster.save_model(temp_booster_path)
    
    # 创建一个新的LGBMClassifier并从保存的booster加载
    updated_model = lgb.LGBMClassifier(**model.get_params())
    
    # 使用一小部分数据来"训练"新模型，这样可以正确初始化所有内部属性
    # 然后用我们的新booster替换它
    sample_size = min(100, len(X_new_scaled))
    updated_model.fit(X_new_scaled[:sample_size], y_new_verified[:sample_size])
    
    # 现在加载我们真正想要的booster
    updated_model._Booster = lgb.Booster(model_file=temp_booster_path)
    
    # 清理临时文件
    os.remove(temp_booster_path)
    
    # 更新全局模型变量
    model = updated_model

    new_model_iteration = new_booster.current_iteration()
    new_model_path = os.path.join(MODEL_DIR, f"lgbm_model_iter_{new_model_iteration}.joblib")
    joblib.dump(model, new_model_path)
    joblib.dump(model, os.path.join(MODEL_DIR, "lgbm_model.joblib")) # 覆盖旧模型，作为最新版
    print(f"模型已更新并保存。最新版本迭代次数: {new_model_iteration}。路径: {new_model_path}")

    # 6. 清理
    # 将已学习过的数据存档，并清空日志文件
    if os.path.exists(REFLECTION_DATA_PATH):
        reflection_df.to_csv(REFLECTION_DATA_PATH, mode='a', header=False)
    else:
        reflection_df.to_csv(REFLECTION_DATA_PATH)
    os.remove(PREDICTION_LOG_PATH) # 【修正】正确的变量名
    print("预测日志已清理，准备下一个学习周期。")

def retrain_with_reflection_realtime():
    """
    真实环境下的反思学习：使用币安API获取历史数据进行验证
    """
    global model
    print("\n--- 开始真实环境反思学习 ---")

    # 1. 加载预测日志
    if not os.path.exists(PREDICTION_LOG_PATH):
        print("没有预测日志，无法进行反思学习。")
        return

    log_df = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True)
    log_df = log_df[~log_df.index.duplicated(keep='last')]

    # 2. 获取足够的历史数据用于验证
    # 获取过去7天的数据，确保能覆盖所有预测记录
    historical_data = fetch_latest_klines_from_binance(limit=336)  # 7天 * 48条/天
    if historical_data is None:
        print("无法获取历史数据进行验证")
        return

    # 3. 计算实际目标值
    actual_targets = prepare_data_for_reflection_realtime(historical_data, PREDICTION_WINDOW)
    if actual_targets is None:
        print("无法计算实际目标值")
        return

    # 4. 合并预测和真实结果
    reflection_df = log_df.join(actual_targets, how='inner')
    reflection_df.dropna(subset=['actual_target'], inplace=True)
    reflection_df['actual_target'] = reflection_df['actual_target'].astype(int)

    if reflection_df.empty:
        print("无法将预测日志与实际结果对齐，请检查时间戳。跳过重训练。")
        return

    reflection_df['is_correct'] = (reflection_df['prediction'] == reflection_df['actual_target']).astype(int)

    print(f"验证了 {len(reflection_df)} 条预测。")
    print(f"其中，正确率: {reflection_df['is_correct'].mean():.2%}")

    # 5. "反思" - 识别错误样本
    error_samples = reflection_df[reflection_df['is_correct'] == 0]
    print(f"识别出 {len(error_samples)} 个错误样本，这些是宝贵的学习资料。")

    if len(error_samples) < 10:
        print("错误样本过少，本次不进行重训练以保持模型稳定。")
        # 清理部分日志，保留最近的预测记录
        cleanup_logs_partial(reflection_df)
        return

    # 6. 进行增量学习（与原函数相同的逻辑）
    X_new_verified = reflection_df[feature_columns]
    y_new_verified = reflection_df['actual_target']

    sample_weights = reflection_df['is_correct'].apply(lambda x: 1 if x == 1 else 3).values
    X_new_scaled = scaler.transform(X_new_verified)

    train_data = lgb.Dataset(X_new_scaled, label=y_new_verified, weight=sample_weights)

    print("\n--- 使用反思数据进行增量训练 ---")

    params = model.get_params()
    params['metric'] = 'binary_logloss'
    params.pop('n_estimators', None)
    params.pop('importance_type', None)

    new_booster = lgb.train(
        params=params,
        train_set=train_data,
        init_model=model,
        num_boost_round=200,
        valid_sets=[train_data],
        callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)]
    )

    # 7. 更新模型（与原函数相同的逻辑）
    temp_booster_path = os.path.join(MODEL_DIR, "temp_booster.txt")
    new_booster.save_model(temp_booster_path)

    updated_model = lgb.LGBMClassifier(**model.get_params())
    sample_size = min(100, len(X_new_scaled))
    updated_model.fit(X_new_scaled[:sample_size], y_new_verified[:sample_size])
    updated_model._Booster = lgb.Booster(model_file=temp_booster_path)

    os.remove(temp_booster_path)
    model = updated_model

    new_model_iteration = new_booster.current_iteration()
    new_model_path = os.path.join(MODEL_DIR, f"lgbm_model_iter_{new_model_iteration}.joblib")
    joblib.dump(model, new_model_path)
    joblib.dump(model, os.path.join(MODEL_DIR, "lgbm_model.joblib"))
    print(f"模型已更新并保存。最新版本迭代次数: {new_model_iteration}。路径: {new_model_path}")

    # 8. 清理日志
    cleanup_logs_partial(reflection_df)

def prepare_data_for_reflection_realtime(historical_data, prediction_window):
    """
    从实时获取的历史数据计算实际目标值
    """
    try:
        df = historical_data.copy()

        # 计算未来回报率
        df['future_return'] = df['close'].pct_change(periods=-prediction_window).shift(-prediction_window)

        # 使用与训练时相同的逻辑计算动态阈值
        threshold = df['future_return'].rolling(window=200, min_periods=50).std().mean() * 0.5
        print(f"使用动态阈值: ±{threshold:.6f} 来验证预测")

        # 定义实际目标
        df['actual_target'] = 0
        df.loc[df['future_return'] > threshold, 'actual_target'] = 1

        return df['actual_target']

    except Exception as e:
        print(f"计算实际目标值失败: {e}")
        return None

def cleanup_logs_partial(reflection_df):
    """
    部分清理日志：将已验证的数据存档，但保留最近的预测记录
    """
    try:
        # 将评估数据存档
        if os.path.exists(REFLECTION_DATA_PATH):
            reflection_df.to_csv(REFLECTION_DATA_PATH, mode='a', header=False)
        else:
            reflection_df.to_csv(REFLECTION_DATA_PATH)

        # 保留最近24小时的预测记录，删除更早的记录
        if os.path.exists(PREDICTION_LOG_PATH):
            log_df = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True)
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_logs = log_df[log_df.index > cutoff_time]

            if not recent_logs.empty:
                recent_logs.to_csv(PREDICTION_LOG_PATH)
                print(f"保留了最近 {len(recent_logs)} 条预测记录")
            else:
                os.remove(PREDICTION_LOG_PATH)
                print("清理了所有预测日志")

        print("反思数据已存档，预测日志已部分清理")

    except Exception as e:
        print(f"清理日志失败: {e}")

# --- 主程序入口 ---
if __name__ == '__main__':
    current_time = datetime.now()

    if REAL_TIME_MODE:
        print(f"\n====== BTC价格预测系统 - 真实环境模式 ======")
        print(f"执行时间: {current_time}")

        # 真实环境模式：从币安API获取最新数据进行预测
        print("\n--- 1. 获取最新市场数据并进行预测 ---")

        # 获取最新K线数据
        latest_klines = fetch_latest_klines_from_binance()
        if latest_klines is None:
            print("无法获取最新数据，退出程序")
            exit(1)

        # 进行预测并记录
        predict_new_data(latest_klines)

        # 检查是否有足够的预测历史进行反思学习
        if os.path.exists(PREDICTION_LOG_PATH):
            prediction_log = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True)
            prediction_count = len(prediction_log)
            print(f"当前累计预测记录: {prediction_count} 条")

            # 如果预测记录足够多（比如超过48条，即24小时的数据），则进行反思学习
            if prediction_count >= 48:
                print("\n--- 2. 开始反思学习 ---")
                # 使用币安API数据进行验证，而不是历史文件
                retrain_with_reflection_realtime()
            else:
                print(f"预测记录不足({prediction_count} < 48)，跳过反思学习")
        else:
            print("没有预测历史，跳过反思学习")

    else:
        print(f"\n====== 开始模拟一个学习周期 ======")

        # 模拟模式：使用历史数据进行测试
        print("\n--- (模拟) 获取用于预测的实时数据 ---")
        full_ohlcv_df = pd.read_csv(FILE_PATH)

        # 假设我们要对数据集中的最后1000个点进行逐一预测，模拟真实交易环境
        # 我们将历史数据分割为"已知历史"和"未来未知"两部分
        split_point = len(full_ohlcv_df) - 1000
        known_history_df = full_ohlcv_df.iloc[:split_point]
        future_unseen_df = full_ohlcv_df.iloc[split_point:]

        # 【关键修正】我们只对"未来未知"的数据进行预测
        # 为了给第一个未知数据点计算特征，需要它之前的历史数据（例如100条）
        data_for_prediction_engine = pd.concat([
            known_history_df.tail(100),
            future_unseen_df
        ])

        data_for_prediction_engine['timestamp_utc'] = pd.to_datetime(data_for_prediction_engine['timestamp_utc'])
        data_for_prediction_engine.set_index('timestamp_utc', inplace=True)

        print(f"预测引擎接收 {len(data_for_prediction_engine)} 条数据，将对其中 {len(future_unseen_df)} 条进行预测。")

        # 2. 第一步：预测新数据并记录
        predict_new_data(data_for_prediction_engine)

        # 3. 第二步：模拟时间流逝，我们获得了真实结果，开始反思和学习
        print("\n--- (模拟) 时间流逝，开始每日学习任务 ---")
        retrain_with_reflection(FILE_PATH, PREDICTION_WINDOW)

        print("\n====== 学习周期完成 ======")

    print(f"\n程序执行完成 - {datetime.now()}")
