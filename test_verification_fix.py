#!/usr/bin/env python3
"""
测试验证函数修复
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# 导入修复后的函数
from continuous_predict_fixed import verify_previous_prediction, fetch_latest_klines_from_binance

def test_verification():
    """测试验证函数"""
    print("🧪 测试验证函数修复...")
    
    # 模拟当前价格和时间戳
    current_price = 117650.32
    current_timestamp = pd.Timestamp('2025-07-16 01:35:00')
    
    try:
        result = verify_previous_prediction(current_price, current_timestamp)
        if result:
            print("✅ 验证函数运行成功!")
            print(f"   预测时间: {result['prediction_time']}")
            print(f"   预测方向: {result['predicted_direction']}")
            print(f"   实际方向: {result['actual_direction']}")
            print(f"   价格变化: {result['price_change']:+.6f}")
            print(f"   预测正确: {result['is_correct']}")
            print(f"   使用阈值: {result['threshold_used']:.6f}")
        else:
            print("⚠️ 验证函数返回None（可能没有找到合适的历史预测）")
    except Exception as e:
        print(f"❌ 验证函数出错: {e}")
        import traceback
        traceback.print_exc()

def test_api_fetch():
    """测试API获取"""
    print("\n🌐 测试API数据获取...")
    try:
        data = fetch_latest_klines_from_binance(limit=100)
        if data is not None:
            print(f"✅ API获取成功，数据量: {len(data)}")
            print(f"   最新价格: {data['close'].iloc[-1]:.2f}")
            print(f"   时间范围: {data.index[0]} 到 {data.index[-1]}")
        else:
            print("❌ API获取失败")
    except Exception as e:
        print(f"❌ API获取出错: {e}")

if __name__ == "__main__":
    test_verification()
    test_api_fetch()
