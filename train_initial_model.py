
import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, f1_score
from imblearn.combine import SMOTEENN
import joblib
import warnings
import os
import ta

warnings.filterwarnings('ignore')

# --- 配置参数 ---
FILE_PATH = "BTCUSDT_30m_2020-01-01_to_2024-12-31_minimal_features.csv"
PREDICTION_WINDOW = 1  # 预测未来1个K线周期
MODEL_DIR = "btc_reflection_model_v1"
os.makedirs(MODEL_DIR, exist_ok=True)

# 定义目标字典，方便后续使用
TARGET_NAMES_DICT = {0: '下跌/横盘', 1: '上涨'}
def create_features(df):
    """
    从原始K线数据创建丰富的特征（使用ta库确保一致性）
    """
    df_feat = df.copy()

    try:
        # 使用 ta 库计算技术指标，确保与预测脚本完全一致
        # 动量指标
        df_feat['momentum_rsi'] = ta.momentum.RSIIndicator(df_feat['close']).rsi()

        # 趋势指标
        macd = ta.trend.MACD(df_feat['close'])
        df_feat['trend_macd'] = macd.macd()
        df_feat['trend_macd_signal'] = macd.macd_signal()
        df_feat['trend_macd_diff'] = macd.macd_diff()

        # 波动率指标 (布林带)
        bb = ta.volatility.BollingerBands(df_feat['close'])
        df_feat['volatility_bbm'] = bb.bollinger_mavg()
        df_feat['volatility_bbh'] = bb.bollinger_hband()
        df_feat['volatility_bbl'] = bb.bollinger_lband()
        df_feat['volatility_bbw'] = bb.bollinger_wband()
        df_feat['volatility_bbp'] = bb.bollinger_pband()

        # ADX 趋势强度指标
        adx = ta.trend.ADXIndicator(df_feat['high'], df_feat['low'], df_feat['close'])
        df_feat['trend_adx'] = adx.adx()
        df_feat['trend_adx_pos'] = adx.adx_pos()
        df_feat['trend_adx_neg'] = adx.adx_neg()

        # 成交量指标
        df_feat['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(df_feat['close'], df_feat['volume']).on_balance_volume()

        # 基础价格特征
        df_feat['price_change'] = df_feat['close'].pct_change()
        df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
        df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

        # 多时间框架特征
        windows = [3, 5, 10, 20, 50, 100]
        for window in windows:
            df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
            df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
            df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
            df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

        # 额外的技术指标（保持与预测脚本一致）
        delta = df_feat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-9)
        df_feat['rsi_14'] = 100 - (100 / (1 + rs))

        ema_fast = df_feat['close'].ewm(span=12).mean()
        ema_slow = df_feat['close'].ewm(span=26).mean()
        df_feat['macd'] = ema_fast - ema_slow
        df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
        df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

        df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
        df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
        df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

        # 清理数据
        df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
        df_feat.dropna(inplace=True)

        return df_feat

    except Exception as e:
        print(f"特征工程过程出错: {e}")
        return pd.DataFrame()
def prepare_data(file_path, prediction_window):
    """
    加载、处理数据并定义目标
    """
    print("\n--- 1. 数据加载与预处理 ---")
    df = pd.read_csv(file_path)
    df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
    df.sort_values('timestamp_utc', inplace=True)
    df.set_index('timestamp_utc', inplace=True)
    df.dropna(subset=['open', 'high', 'low', 'close', 'volume'], inplace=True)
    print(f"原始数据加载成功，共 {len(df)} 条记录")

    print("\n--- 2. 高级特征工程 ---")
    df_featured = create_features(df)
    
    print("\n--- 3. 智能目标定义 ---")
    df_featured['future_return'] = df_featured['close'].pct_change(periods=-prediction_window).shift(-prediction_window)
    
    # 动态阈值：上涨或下跌超过近期波动的一小部分才被视为有效信号
    threshold = df_featured['future_return'].rolling(window=2000, min_periods=500).std().mean() * 0.5
    print(f"使用动态阈值: ±{threshold:.6f}")

    df_featured['target'] = 0
    df_featured.loc[df_featured['future_return'] > threshold, 'target'] = 1
    # 对于这个二分类问题，我们可以暂时不区分下跌和横盘
    # df_featured.loc[df_featured['future_return'] < -threshold, 'target'] = 0 

    df_featured.dropna(inplace=True)
    print(f"特征工程与目标定义完成，共 {len(df_featured)} 条有效记录")

    # 【关键检查点】确保这一行是正确的
    feature_columns = [col for col in df_featured.columns if col not in [
        'target', 'future_return',
        'open', 'high', 'low', 'close', 'volume' # 原始价格数据也应排除
    ]]

    # 【关键检查点】确保 X 是严格按照 feature_columns 来创建的
    X = df_featured[feature_columns]
    y = df_featured['target']

    # 打印一些信息来验证
    print(f"创建了 {len(feature_columns)} 个特征。")
    if 'future_return' in X.columns:
        print("\n【严重错误】: 'future_return' 泄露到了特征集 X 中！")
    else:
        print("\n【检查通过】: 'future_return' 未泄露到特征集 X 中。")

    return X, y, feature_columns

def train_and_evaluate(X, y, feature_columns):
    """
    执行模型训练、评估和保存
    """
    print("\n--- 4. 数据集分割 (时间序列) ---")
    train_size = int(len(X) * 0.8)
    X_train, X_test = X.iloc[:train_size], X.iloc[train_size:]
    y_train, y_test = y.iloc[:train_size], y.iloc[train_size:]
    print(f"训练集: {len(X_train)} | 测试集: {len(X_test)}")

    print("\n--- 5. 特征缩放 ---")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print("\n--- 6. 数据平衡 (SMOTEENN) ---")
    smoteenn = SMOTEENN(random_state=42)
    X_train_balanced, y_train_balanced = smoteenn.fit_resample(X_train_scaled, y_train)
    print(f"平衡后训练集大小: {X_train_balanced.shape[0]}")

    print("\n--- 7. 模型训练 (LightGBM) ---")
    lgb_model = lgb.LGBMClassifier(
        objective='binary',
        metric='binary_logloss',
        n_estimators=1000,
        learning_rate=0.05,
        num_leaves=31,
        max_depth=-1,
        min_child_samples=20,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        n_jobs=-1
    )

    # 使用早停法防止过拟合
    lgb_model.fit(X_train_balanced, y_train_balanced,
                  eval_set=[(scaler.transform(X_test), y_test)], # 在原始测试集上验证
                  eval_metric='f1',
                  callbacks=[lgb.early_stopping(100, verbose=False)])

    print("\n--- 8. 最终评估 ---")
    test_predictions = lgb_model.predict(X_test_scaled)
    test_accuracy = accuracy_score(y_test, test_predictions)
    test_f1 = f1_score(y_test, test_predictions, average='weighted')

    print(f"测试集准确率: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
    print(f"测试集F1分数: {test_f1:.4f}")
    print("\n分类报告:")
    print(classification_report(y_test, test_predictions, target_names=list(TARGET_NAMES_DICT.values())))
    print("\n混淆矩阵:")
    print(confusion_matrix(y_test, test_predictions))

    print("\n--- 9. 保存模型及预处理器 ---")
    joblib.dump(lgb_model, os.path.join(MODEL_DIR, "lgbm_model.joblib"))
    joblib.dump(scaler, os.path.join(MODEL_DIR, "scaler.joblib"))
    joblib.dump(feature_columns, os.path.join(MODEL_DIR, "feature_columns.joblib"))
    print("模型、缩放器和特征列表已保存。")
    
    return lgb_model

# --- 主程序入口 ---
if __name__ == '__main__':
    print("====== 开始执行初始模型训练 ======")
    X_data, y_data, features = prepare_data(FILE_PATH, PREDICTION_WINDOW)
    model = train_and_evaluate(X_data, y_data, features)
    print("\n====== 初始模型训练完成 ======")

