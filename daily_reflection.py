#!/usr/bin/env python3
"""
BTC价格预测反思学习脚本
功能：
1. 验证之前的预测结果准确性
2. 从错误中学习，进行增量训练
3. 更新模型以提高未来预测准确性
4. 清理预测日志，为下一轮预测做准备

使用方法：
python daily_reflection.py

或设置crontab定时任务（建议每天凌晨执行）：
0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import requests
import warnings
import ta
from datetime import datetime, timedelta

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置参数 ---
MODEL_DIR = "btc_reflection_model_v1"
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
REFLECTION_DATA_PATH = os.path.join(MODEL_DIR, "reflection_data.csv")
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"
PREDICTION_WINDOW = 1  # 预测未来1个周期（30分钟）

# 反思学习配置
MIN_PREDICTION_SAMPLES = 24  # 最少需要24条预测记录（12小时）
MIN_ERROR_SAMPLES = 5       # 最少错误样本数量
ACCURACY_THRESHOLD = 0.55   # 准确率阈值，低于此值触发重训练
DAYS_BACK = 7              # 获取历史数据的天数

def create_features(df):
    """
    从原始K线数据创建特征（与训练和预测脚本完全一致）
    """
    df_feat = df.copy()

    try:
        # 使用 ta 库计算技术指标，确保与训练和预测脚本完全一致
        # 动量指标
        df_feat['momentum_rsi'] = ta.momentum.RSIIndicator(df_feat['close']).rsi()

        # 趋势指标
        macd = ta.trend.MACD(df_feat['close'])
        df_feat['trend_macd'] = macd.macd()
        df_feat['trend_macd_signal'] = macd.macd_signal()
        df_feat['trend_macd_diff'] = macd.macd_diff()

        # 波动率指标 (布林带)
        bb = ta.volatility.BollingerBands(df_feat['close'])
        df_feat['volatility_bbm'] = bb.bollinger_mavg()
        df_feat['volatility_bbh'] = bb.bollinger_hband()
        df_feat['volatility_bbl'] = bb.bollinger_lband()
        df_feat['volatility_bbw'] = bb.bollinger_wband()
        df_feat['volatility_bbp'] = bb.bollinger_pband()

        # ADX 趋势强度指标
        adx = ta.trend.ADXIndicator(df_feat['high'], df_feat['low'], df_feat['close'])
        df_feat['trend_adx'] = adx.adx()
        df_feat['trend_adx_pos'] = adx.adx_pos()
        df_feat['trend_adx_neg'] = adx.adx_neg()

        # 成交量指标
        df_feat['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(df_feat['close'], df_feat['volume']).on_balance_volume()

        # 基础价格特征
        df_feat['price_change'] = df_feat['close'].pct_change()
        df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
        df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

        # 多时间框架特征
        windows = [3, 5, 10, 20, 50, 100]
        for window in windows:
            df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
            df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
            df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
            df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

        # 额外的技术指标（保持与训练和预测脚本一致）
        delta = df_feat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-9)
        df_feat['rsi_14'] = 100 - (100 / (1 + rs))

        ema_fast = df_feat['close'].ewm(span=12).mean()
        ema_slow = df_feat['close'].ewm(span=26).mean()
        df_feat['macd'] = ema_fast - ema_slow
        df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
        df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

        df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
        df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
        df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

        # 清理数据
        df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
        df_feat.dropna(inplace=True)

        return df_feat

    except Exception as e:
        print(f"特征工程过程出错: {e}")
        return pd.DataFrame()

def fetch_historical_klines(symbol='BTCUSDT', interval='30m', days_back=DAYS_BACK):
    """
    获取历史K线数据用于验证预测结果（支持大量数据分批获取）

    Args:
        symbol: 交易对
        interval: K线间隔
        days_back: 获取过去几天的数据

    Returns:
        pandas.DataFrame: 历史K线数据
    """
    try:
        # 计算需要获取的K线数量
        # 30分钟间隔，每天48根K线
        total_limit = days_back * 48

        print(f"正在获取过去{days_back}天的历史K线数据（需要{total_limit}条）...")

        all_data = []

        # 如果需要的数据量超过1000条，分批获取
        if total_limit <= 1000:
            # 单次获取
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': total_limit
            }

            response = requests.get(BINANCE_API_URL, params=params, timeout=10)
            response.raise_for_status()
            all_data = response.json()

        else:
            # 分批获取
            print(f"数据量较大，将分批获取...")
            remaining = total_limit
            end_time = None

            while remaining > 0:
                batch_limit = min(1000, remaining)

                params = {
                    'symbol': symbol,
                    'interval': interval,
                    'limit': batch_limit
                }

                # 如果不是第一批，设置结束时间
                if end_time is not None:
                    params['endTime'] = end_time

                response = requests.get(BINANCE_API_URL, params=params, timeout=10)
                response.raise_for_status()
                batch_data = response.json()

                if not batch_data:
                    break

                # 将新数据添加到开头（因为我们是从最新往回获取）
                all_data = batch_data + all_data

                # 更新结束时间为当前批次最早的时间
                end_time = batch_data[0][0] - 1  # 减1毫秒避免重复
                remaining -= len(batch_data)

                print(f"已获取 {len(all_data)} 条数据，剩余 {remaining} 条...")

                # 避免请求过于频繁
                import time
                time.sleep(0.1)

        if not all_data:
            print("未获取到任何数据")
            return None

        # 转换为DataFrame
        df = pd.DataFrame(all_data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # 数据类型转换
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'number_of_trades']:
            df[col] = df[col].astype(float)

        # 重命名和选择需要的列（包含number_of_trades以保持与预测脚本一致）
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'number_of_trades']].copy()
        df.rename(columns={'timestamp': 'timestamp_utc'}, inplace=True)
        df.set_index('timestamp_utc', inplace=True)

        # 按时间排序并去重
        df = df.sort_index().drop_duplicates()

        print(f"成功获取 {len(df)} 条历史K线数据，时间范围: {df.index[0]} 到 {df.index[-1]}")
        return df

    except Exception as e:
        print(f"获取历史数据失败: {e}")
        return None

# 删除了prepare_actual_targets函数，因为我们直接使用预测日志中的验证结果

def load_prediction_log():
    """
    加载预测日志，只处理已验证的预测结果
    """
    try:
        if not os.path.exists(PREDICTION_LOG_PATH):
            print("没有找到预测日志文件")
            return None

        log_df = pd.read_csv(PREDICTION_LOG_PATH)

        # 确保时间戳格式正确
        log_df['timestamp_utc'] = pd.to_datetime(log_df['timestamp_utc'], format='%Y-%m-%d %H:%M:%S', errors='coerce')
        log_df = log_df.dropna(subset=['timestamp_utc'])
        log_df.set_index('timestamp_utc', inplace=True)
        log_df = log_df[~log_df.index.duplicated(keep='last')]  # 去重

        # 只保留已验证的预测（有is_correct值的记录）
        verified_df = log_df.dropna(subset=['is_correct'])

        print(f"成功加载 {len(log_df)} 条预测记录，其中 {len(verified_df)} 条已验证")
        return verified_df

    except Exception as e:
        print(f"加载预测日志失败: {e}")
        return None

def analyze_verified_predictions(verified_log):
    """
    分析已验证的预测结果，直接使用预测日志中的验证结果

    Args:
        verified_log: 已验证的预测日志DataFrame

    Returns:
        dict: 分析结果
    """
    try:
        if verified_log.empty:
            print("没有已验证的预测记录")
            return None

        # 直接使用预测日志中的验证结果
        total_predictions = len(verified_log)
        correct_predictions = verified_log['is_correct'].sum()
        accuracy = correct_predictions / total_predictions

        # 找出错误预测
        error_samples = verified_log[verified_log['is_correct'] == False].copy()

        result = {
            'total_predictions': total_predictions,
            'correct_predictions': int(correct_predictions),
            'accuracy': accuracy,
            'error_samples': error_samples,
            'all_verified': verified_log
        }

        print(f"分析了 {total_predictions} 条已验证预测")
        print(f"正确预测: {int(correct_predictions)} 条")
        print(f"准确率: {accuracy:.2%}")
        print(f"错误样本: {len(error_samples)} 条")

        return result

    except Exception as e:
        print(f"分析预测结果失败: {e}")
        return None

def get_features_for_error_samples(error_samples):
    """
    为错误样本获取对应的市场特征数据

    Args:
        error_samples: 错误预测样本DataFrame

    Returns:
        DataFrame: 包含特征的错误样本数据
    """
    try:
        if error_samples.empty:
            return pd.DataFrame()

        print(f"为 {len(error_samples)} 个错误样本获取特征数据...")

        # 获取错误样本的时间范围
        start_time = error_samples.index.min() - timedelta(hours=24)  # 提前24小时获取数据用于特征计算
        end_time = error_samples.index.max() + timedelta(hours=1)

        # 计算需要的数据量（小时数 * 2，因为30分钟间隔）
        hours_needed = int((end_time - start_time).total_seconds() / 3600)
        klines_needed = hours_needed * 2 + 100  # 额外100条用于特征计算

        # 获取历史数据
        historical_data = fetch_historical_klines(days_back=min(7, max(1, hours_needed // 24 + 1)))

        if historical_data is None or historical_data.empty:
            print("无法获取历史数据")
            return pd.DataFrame()

        # 计算特征
        featured_data = create_features(historical_data)

        if featured_data.empty:
            print("特征计算失败")
            return pd.DataFrame()

        # 将错误样本与特征数据合并
        error_with_features = error_samples.join(featured_data, how='inner')

        print(f"成功为 {len(error_with_features)} 个错误样本获取了特征")
        return error_with_features

    except Exception as e:
        print(f"获取错误样本特征失败: {e}")
        return pd.DataFrame()

def retrain_model_with_errors(error_samples_with_features, min_error_samples=5):
    """
    使用错误样本进行增量训练（简化版）

    Args:
        error_samples_with_features: 包含特征的错误样本DataFrame
        min_error_samples: 最少错误样本数量

    Returns:
        bool: 是否成功重训练
    """
    try:
        if len(error_samples_with_features) < min_error_samples:
            print(f"错误样本数量({len(error_samples_with_features)})少于最小要求({min_error_samples})，跳过重训练")
            return False

        print(f"使用 {len(error_samples_with_features)} 个错误样本进行增量训练")

        # 加载模型和工具
        model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
        scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
        feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))

        # 检查特征列是否存在
        missing_features = set(feature_columns) - set(error_samples_with_features.columns)
        if missing_features:
            print(f"缺少特征列: {missing_features}")
            return False

        # 准备训练数据
        X_errors = error_samples_with_features[feature_columns]
        # 使用price_change来重新计算正确的标签
        y_errors = (error_samples_with_features['price_change'] > 0).astype(int)

        # 标准化特征
        X_errors_scaled = scaler.transform(X_errors)

        # 简化的增量训练：只用错误样本进行少量迭代
        model.fit(X_errors_scaled, y_errors,
                 sample_weight=np.ones(len(y_errors)) * 3)  # 给错误样本更高权重

        # 保存更新后的模型
        joblib.dump(model, os.path.join(MODEL_DIR, "lgbm_model.joblib"))

        print("✅ 模型增量训练完成")
        return True

    except Exception as e:
        print(f"增量训练失败: {e}")
        return False

def cleanup_logs(verified_log):
    """
    清理日志文件，将已处理的数据存档
    """
    try:
        # 将已验证的数据存档
        if os.path.exists(REFLECTION_DATA_PATH):
            verified_log.to_csv(REFLECTION_DATA_PATH, mode='a', header=False)
        else:
            verified_log.to_csv(REFLECTION_DATA_PATH)

        # 清空预测日志
        if os.path.exists(PREDICTION_LOG_PATH):
            os.remove(PREDICTION_LOG_PATH)

        print("日志已清理，反思数据已存档")

    except Exception as e:
        print(f"清理日志失败: {e}")

def main():
    """
    主函数：执行简化的反思学习流程
    """
    print(f"\n=== BTC价格预测反思学习 - {datetime.now()} ===")

    # 1. 加载已验证的预测日志
    verified_log = load_prediction_log()
    if verified_log is None or verified_log.empty:
        print("没有已验证的预测记录，无法进行反思学习")
        return

    if len(verified_log) < MIN_PREDICTION_SAMPLES:
        print(f"已验证样本数量({len(verified_log)})不足，需要至少{MIN_PREDICTION_SAMPLES}条记录")
        print("跳过本次反思学习，等待更多验证数据")
        return

    # 2. 分析已验证的预测结果
    print(f"\n--- 分析预测结果 ---")
    analysis_result = analyze_verified_predictions(verified_log)
    if analysis_result is None:
        print("预测分析失败，跳过反思学习")
        return

    # 3. 根据分析结果决定是否重训练
    accuracy = analysis_result['accuracy']
    error_count = len(analysis_result['error_samples'])

    print(f"\n--- 反思学习决策 ---")
    print(f"当前准确率: {accuracy:.2%}")
    print(f"错误样本数: {error_count}")
    print(f"准确率阈值: {ACCURACY_THRESHOLD:.1%}")
    print(f"最少错误样本要求: {MIN_ERROR_SAMPLES}")

    should_retrain = (accuracy < ACCURACY_THRESHOLD and error_count >= MIN_ERROR_SAMPLES)

    if should_retrain:
        print(f"\n--- 开始增量学习 ---")
        print("触发重训练条件，分析错误样本...")

        # 为错误样本获取特征
        error_samples_with_features = get_features_for_error_samples(analysis_result['error_samples'])

        if not error_samples_with_features.empty:
            retrain_success = retrain_model_with_errors(error_samples_with_features, MIN_ERROR_SAMPLES)
            if retrain_success:
                print("✅ 模型重训练成功")
            else:
                print("❌ 模型重训练失败")
        else:
            print("❌ 无法获取错误样本特征，跳过重训练")
    else:
        if accuracy >= ACCURACY_THRESHOLD:
            print(f"✅ 准确率({accuracy:.2%})良好，暂不重训练")
        else:
            print(f"⚠️ 准确率较低但错误样本不足，暂不重训练")

    # 4. 清理日志
    print(f"\n--- 清理预测日志 ---")
    cleanup_logs(analysis_result['all_verified'])

    print(f"\n🎉 反思学习完成 - {datetime.now()}")

if __name__ == "__main__":
    main()
