#!/usr/bin/env python3
"""
BTC价格预测系统监控脚本
功能：
1. 监控系统运行状态
2. 分析预测准确率趋势
3. 检查模型性能指标
4. 生成系统健康报告

使用方法：
python monitor_system.py

或设置crontab定时任务（建议每小时执行）：
0 * * * * cd /ai-model && python monitor_system.py >> logs/monitor.log 2>&1
"""

import pandas as pd
import numpy as np
import os
import json
import warnings
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

# --- 配置参数 ---
MODEL_DIR = "btc_reflection_model_v1"
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
REFLECTION_DATA_PATH = os.path.join(MODEL_DIR, "reflection_data.csv")
MONITOR_REPORT_PATH = os.path.join(MODEL_DIR, "system_monitor_report.json")
LOGS_DIR = "logs"

# 监控阈值
MIN_PREDICTION_FREQUENCY = 24  # 24小时内最少预测次数
ACCURACY_WARNING_THRESHOLD = 0.50  # 准确率警告阈值
ACCURACY_CRITICAL_THRESHOLD = 0.40  # 准确率严重警告阈值

def check_system_files():
    """
    检查系统关键文件是否存在
    
    Returns:
        dict: 文件检查结果
    """
    files_to_check = {
        'model_file': os.path.join(MODEL_DIR, "lgbm_model.joblib"),
        'scaler_file': os.path.join(MODEL_DIR, "scaler.joblib"),
        'feature_columns_file': os.path.join(MODEL_DIR, "feature_columns.joblib"),
        'prediction_log': PREDICTION_LOG_PATH,
        'reflection_data': REFLECTION_DATA_PATH
    }
    
    results = {}
    for name, path in files_to_check.items():
        results[name] = {
            'exists': os.path.exists(path),
            'path': path,
            'size': os.path.getsize(path) if os.path.exists(path) else 0,
            'modified': datetime.fromtimestamp(os.path.getmtime(path)).isoformat() if os.path.exists(path) else None
        }
    
    return results

def analyze_prediction_activity():
    """
    分析预测活动情况
    
    Returns:
        dict: 预测活动分析结果
    """
    try:
        if not os.path.exists(PREDICTION_LOG_PATH):
            return {
                'status': 'no_data',
                'message': '没有预测日志文件',
                'total_predictions': 0,
                'recent_predictions': 0
            }
        
        df = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True, on_bad_lines='skip')
        
        # 基本统计
        total_predictions = len(df)
        
        # 最近24小时的预测
        cutoff_time = datetime.now() - timedelta(hours=24)
        recent_df = df[df.index > cutoff_time]
        recent_predictions = len(recent_df)
        
        # 预测频率分析
        if total_predictions > 0:
            time_span = (df.index.max() - df.index.min()).total_seconds() / 3600  # 小时
            avg_frequency = total_predictions / time_span if time_span > 0 else 0
            
            # 最后一次预测时间
            last_prediction = df.index.max()
            hours_since_last = (datetime.now() - last_prediction).total_seconds() / 3600
        else:
            avg_frequency = 0
            last_prediction = None
            hours_since_last = float('inf')
        
        # 状态评估
        if recent_predictions >= MIN_PREDICTION_FREQUENCY:
            status = 'healthy'
            message = '预测活动正常'
        elif recent_predictions > 0:
            status = 'warning'
            message = f'预测频率较低，24小时内仅{recent_predictions}次预测'
        else:
            status = 'critical'
            message = '24小时内无预测活动'
        
        return {
            'status': status,
            'message': message,
            'total_predictions': total_predictions,
            'recent_predictions': recent_predictions,
            'avg_frequency_per_hour': round(avg_frequency, 2),
            'last_prediction': last_prediction.isoformat() if last_prediction else None,
            'hours_since_last': round(hours_since_last, 2)
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': f'分析预测活动失败: {e}',
            'total_predictions': 0,
            'recent_predictions': 0
        }

def analyze_prediction_accuracy():
    """
    分析预测准确率
    
    Returns:
        dict: 准确率分析结果
    """
    try:
        if not os.path.exists(REFLECTION_DATA_PATH):
            return {
                'status': 'no_data',
                'message': '没有反思数据，无法分析准确率',
                'overall_accuracy': None,
                'recent_accuracy': None
            }
        
        df = pd.read_csv(REFLECTION_DATA_PATH, index_col=0, parse_dates=True, on_bad_lines='skip')
        
        if df.empty or 'is_correct' not in df.columns:
            return {
                'status': 'no_data',
                'message': '反思数据不完整',
                'overall_accuracy': None,
                'recent_accuracy': None
            }
        
        # 总体准确率
        overall_accuracy = df['is_correct'].mean()
        
        # 最近7天的准确率
        cutoff_time = datetime.now() - timedelta(days=7)
        # 处理时区问题
        if df.index.tz is not None:
            cutoff_time = cutoff_time.replace(tzinfo=df.index.tz)
        recent_df = df[df.index > cutoff_time]
        recent_accuracy = recent_df['is_correct'].mean() if not recent_df.empty else None
        
        # 准确率趋势（按天分组）
        daily_accuracy = df.groupby(df.index.date)['is_correct'].mean()
        
        # 状态评估
        current_accuracy = recent_accuracy if recent_accuracy is not None else overall_accuracy
        
        if current_accuracy is None:
            status = 'no_data'
            message = '无足够数据评估准确率'
        elif current_accuracy >= ACCURACY_WARNING_THRESHOLD:
            status = 'healthy'
            message = '预测准确率良好'
        elif current_accuracy >= ACCURACY_CRITICAL_THRESHOLD:
            status = 'warning'
            message = f'预测准确率偏低: {current_accuracy:.2%}'
        else:
            status = 'critical'
            message = f'预测准确率严重偏低: {current_accuracy:.2%}'
        
        return {
            'status': status,
            'message': message,
            'overall_accuracy': round(overall_accuracy, 4) if overall_accuracy is not None else None,
            'recent_accuracy': round(recent_accuracy, 4) if recent_accuracy is not None else None,
            'total_evaluations': len(df),
            'recent_evaluations': len(recent_df),
            'daily_accuracy_trend': {str(k): v for k, v in daily_accuracy.tail(7).to_dict().items()} if not daily_accuracy.empty else {}
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': f'分析准确率失败: {e}',
            'overall_accuracy': None,
            'recent_accuracy': None
        }

def check_model_versions():
    """
    检查模型版本信息
    
    Returns:
        dict: 模型版本信息
    """
    try:
        model_files = []
        
        # 查找所有模型文件
        for file in os.listdir(MODEL_DIR):
            if file.startswith('lgbm_model') and file.endswith('.joblib'):
                file_path = os.path.join(MODEL_DIR, file)
                model_files.append({
                    'filename': file,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified': datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                })
        
        # 按修改时间排序
        model_files.sort(key=lambda x: x['modified'], reverse=True)
        
        return {
            'status': 'success',
            'total_versions': len(model_files),
            'latest_model': model_files[0] if model_files else None,
            'all_versions': model_files
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': f'检查模型版本失败: {e}',
            'total_versions': 0
        }

def generate_system_report():
    """
    生成系统监控报告
    
    Returns:
        dict: 完整的系统报告
    """
    report = {
        'timestamp': datetime.now().isoformat(),
        'system_files': check_system_files(),
        'prediction_activity': analyze_prediction_activity(),
        'prediction_accuracy': analyze_prediction_accuracy(),
        'model_versions': check_model_versions()
    }
    
    # 计算总体系统状态
    statuses = [
        report['prediction_activity']['status'],
        report['prediction_accuracy']['status'],
        report['model_versions']['status']
    ]
    
    if 'critical' in statuses:
        overall_status = 'critical'
    elif 'warning' in statuses:
        overall_status = 'warning'
    elif 'error' in statuses:
        overall_status = 'error'
    else:
        overall_status = 'healthy'
    
    report['overall_status'] = overall_status
    
    return report

def save_report(report):
    """
    保存监控报告到文件
    
    Args:
        report: 监控报告字典
    """
    try:
        with open(MONITOR_REPORT_PATH, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"监控报告已保存到: {MONITOR_REPORT_PATH}")
    except Exception as e:
        print(f"保存监控报告失败: {e}")

def print_summary(report):
    """
    打印监控报告摘要
    
    Args:
        report: 监控报告字典
    """
    print(f"\n=== BTC价格预测系统监控报告 ===")
    print(f"生成时间: {report['timestamp']}")
    print(f"总体状态: {report['overall_status'].upper()}")
    
    print(f"\n--- 预测活动 ---")
    activity = report['prediction_activity']
    print(f"状态: {activity['status']}")
    print(f"消息: {activity['message']}")
    print(f"总预测次数: {activity['total_predictions']}")
    print(f"24小时内预测: {activity['recent_predictions']}")
    
    print(f"\n--- 预测准确率 ---")
    accuracy = report['prediction_accuracy']
    print(f"状态: {accuracy['status']}")
    print(f"消息: {accuracy['message']}")
    if accuracy['overall_accuracy'] is not None:
        print(f"总体准确率: {accuracy['overall_accuracy']:.2%}")
    if accuracy['recent_accuracy'] is not None:
        print(f"近期准确率: {accuracy['recent_accuracy']:.2%}")
    
    print(f"\n--- 模型版本 ---")
    models = report['model_versions']
    print(f"模型版本数: {models['total_versions']}")
    if models.get('latest_model'):
        latest = models['latest_model']
        print(f"最新模型: {latest['filename']}")
        print(f"更新时间: {latest['modified']}")

def main():
    """
    主函数：执行系统监控
    """
    print(f"\n=== 系统监控开始 - {datetime.now()} ===")
    
    # 生成监控报告
    report = generate_system_report()
    
    # 保存报告
    save_report(report)
    
    # 打印摘要
    print_summary(report)
    
    print(f"\n=== 系统监控完成 - {datetime.now()} ===")

if __name__ == "__main__":
    main()
