# BTC价格预测系统 - 最终优化版定时任务配置
# 
# 推荐使用方式：
# 方式1（推荐）：使用持续运行脚本
#   - 手动启动：nohup python continuous_predict.py > logs/continuous_predict.log 2>&1 &
#   - 只需要设置反思学习和监控的定时任务
#
# 方式2：使用传统定时任务
#   - 设置预测、反思学习和监控的定时任务
#
# 注意：请根据实际Python环境路径修改python路径

# ============================================================================
# 方式1：持续运行模式（推荐）
# ============================================================================

# 每天凌晨2点进行反思学习（分析预测结果，优化模型）
0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控（检查系统状态和性能）
0 * * * * cd /ai-model && python monitor_system.py >> logs/monitor.log 2>&1

# 每周日凌晨3点清理旧日志文件（可选）
0 3 * * 0 cd /ai-model && find logs/ -name "*.log" -mtime +30 -delete

# 系统重启时自动启动持续预测脚本（可选）
@reboot cd /ai-model && nohup python continuous_predict.py > logs/continuous_predict.log 2>&1 &

# ============================================================================
# 方式2：传统定时任务模式
# ============================================================================

# 如果不使用持续运行模式，可以启用以下配置：

# 每30分钟进行一次BTC价格预测
# */30 * * * * cd /ai-model && python real_time_predict.py >> logs/predict.log 2>&1

# 每天凌晨2点进行反思学习
# 0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控
# 0 * * * * cd /ai-model && python monitor_system.py >> logs/monitor.log 2>&1

# 每周日凌晨3点清理旧日志文件
# 0 3 * * 0 cd /ai-model && find logs/ -name "*.log" -mtime +30 -delete

# ============================================================================
# 使用说明
# ============================================================================

# 1. 编辑crontab：
#    crontab -e

# 2. 将上述配置（选择方式1或方式2）添加到crontab文件中

# 3. 保存并退出

# 4. 验证crontab设置：
#    crontab -l

# 5. 查看cron日志：
#    tail -f /var/log/cron

# ============================================================================
# 监控命令
# ============================================================================

# 查看预测日志：
# tail -f logs/predict.log

# 查看反思学习日志：
# tail -f logs/reflection.log

# 查看监控日志：
# tail -f logs/monitor.log

# 查看持续预测日志（方式1）：
# tail -f logs/continuous_predict.log

# 检查持续预测进程状态：
# ps aux | grep continuous_predict.py

# 停止持续预测进程：
# pkill -f continuous_predict.py

# 重启持续预测进程：
# cd /ai-model && nohup python continuous_predict.py > logs/continuous_predict.log 2>&1 &
