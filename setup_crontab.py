#!/usr/bin/env python3
"""
设置定时任务脚本
用于配置BTC价格预测系统的定时执行
"""

import os
import sys
import subprocess
from datetime import datetime

def setup_crontab_for_reflection():
    """
    设置反思学习系统的定时任务
    """
    current_dir = os.getcwd()
    python_path = sys.executable
    
    print("=== BTC价格预测系统 - 定时任务配置 ===")
    print(f"当前目录: {current_dir}")
    print(f"Python路径: {python_path}")
    print(f"配置时间: {datetime.now()}")
    
    # 定时任务配置
    crontab_entries = [
        "# BTC价格预测系统 - 反思学习定时任务",
        "# 每30分钟执行一次预测（实时模式）",
        f"*/30 * * * * cd {current_dir} && {python_path} reflection_cycle_fixed.py >> logs/prediction.log 2>&1",
        "",
        "# 每天凌晨2点执行反思学习（如果有足够的预测数据）",
        f"0 2 * * * cd {current_dir} && {python_path} reflection_cycle_fixed.py >> logs/reflection.log 2>&1",
        ""
    ]
    
    crontab_content = "\n".join(crontab_entries)
    
    print("\n建议的crontab配置:")
    print("-" * 60)
    print(crontab_content)
    print("-" * 60)
    
    # 保存配置到文件
    config_file = "crontab_reflection.txt"
    with open(config_file, "w") as f:
        f.write(crontab_content)
    
    print(f"\n配置已保存到: {config_file}")
    
    print("\n手动设置步骤:")
    print("1. 运行命令: crontab -e")
    print("2. 将上述配置添加到crontab文件中")
    print("3. 保存并退出")
    
    print("\n自动设置选项:")
    response = input("是否要自动添加到crontab? (y/n): ").lower().strip()
    
    if response == 'y':
        try:
            # 获取当前crontab
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            current_crontab = result.stdout if result.returncode == 0 else ""
            
            # 检查是否已经存在相关配置
            if "reflection_cycle_fixed.py" in current_crontab:
                print("检测到已存在相关配置，跳过自动设置")
                return
            
            # 添加新配置
            new_crontab = current_crontab + "\n" + crontab_content
            
            # 写入新的crontab
            process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
            process.communicate(input=new_crontab)
            
            if process.returncode == 0:
                print("✅ 定时任务已自动添加到crontab")
            else:
                print("❌ 自动添加失败，请手动设置")
                
        except Exception as e:
            print(f"❌ 自动设置失败: {e}")
            print("请手动设置定时任务")
    
    print("\n监控命令:")
    print("# 查看预测日志")
    print("tail -f logs/prediction.log")
    print("\n# 查看反思学习日志")
    print("tail -f logs/reflection.log")
    print("\n# 检查crontab状态")
    print("crontab -l")

def create_log_directories():
    """
    创建必要的日志目录
    """
    directories = ["logs", "btc_reflection_model_v1"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录已存在: {directory}")

def test_reflection_script():
    """
    测试反思脚本是否能正常运行
    """
    print("\n测试反思脚本...")
    try:
        # 先设置为模拟模式进行测试
        print("正在测试脚本运行...")
        result = subprocess.run([sys.executable, "reflection_cycle_fixed.py"], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 反思脚本测试通过")
            return True
        else:
            print(f"❌ 反思脚本测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本运行超时")
        return False
    except Exception as e:
        print(f"❌ 脚本测试失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("开始配置BTC价格预测系统...")
    
    # 1. 创建日志目录
    print("\n1. 创建日志目录")
    create_log_directories()
    
    # 2. 测试脚本
    print("\n2. 测试反思脚本")
    if not test_reflection_script():
        print("脚本测试失败，请检查配置后重试")
        return
    
    # 3. 设置定时任务
    print("\n3. 设置定时任务")
    setup_crontab_for_reflection()
    
    print("\n🎉 配置完成！")
    print("\n系统将按以下计划运行:")
    print("- 每30分钟: 获取最新数据并进行预测")
    print("- 每天凌晨2点: 如果有足够数据则进行反思学习")
    
    print("\n重要提示:")
    print("1. 确保网络连接正常，能访问币安API")
    print("2. 定期检查日志文件")
    print("3. 监控模型性能和预测准确率")

if __name__ == "__main__":
    main()
