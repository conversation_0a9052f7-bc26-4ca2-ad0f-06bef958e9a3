#!/usr/bin/env python3
"""
生产环境部署配置脚本
功能：
1. 检查系统依赖
2. 验证模型文件
3. 设置定时任务
4. 创建日志目录
"""

import os
import sys
import subprocess
import joblib
from datetime import datetime

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'pandas', 'numpy', 'lightgbm', 'joblib', 'requests', 'sklearn'
    ]
    
    print("检查依赖包...")
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    print("所有依赖包检查通过！")
    return True

def check_model_files():
    """检查模型文件是否存在"""
    model_dir = "btc_reflection_model_v1"
    required_files = [
        "lgbm_model.joblib",
        "scaler.joblib", 
        "feature_columns.joblib"
    ]
    
    print("\n检查模型文件...")
    
    if not os.path.exists(model_dir):
        print(f"✗ 模型目录 {model_dir} 不存在")
        print("请先运行 'python train_initial_model.py' 训练初始模型")
        return False
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(model_dir, file)
        if os.path.exists(file_path):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print("请先运行 'python train_initial_model.py' 训练初始模型")
        return False
    
    # 测试加载模型
    try:
        model = joblib.load(os.path.join(model_dir, "lgbm_model.joblib"))
        scaler = joblib.load(os.path.join(model_dir, "scaler.joblib"))
        feature_columns = joblib.load(os.path.join(model_dir, "feature_columns.joblib"))
        print(f"✓ 模型加载测试通过，特征数量: {len(feature_columns)}")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    print("模型文件检查通过！")
    return True

def test_api_connection():
    """测试币安API连接"""
    print("\n测试币安API连接...")
    try:
        import requests
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
        if response.status_code == 200:
            print("✓ 币安API连接正常")
            return True
        else:
            print(f"✗ 币安API响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 币安API连接失败: {e}")
        return False

def create_log_directories():
    """创建日志目录"""
    print("\n创建日志目录...")

    directories = [
        "logs",
        "btc_reflection_model_v1"
    ]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ 创建目录: {directory}")
        else:
            print(f"✓ 目录已存在: {directory}")

    return True

def setup_crontab():
    """设置定时任务"""
    print("\n设置定时任务...")
    
    current_dir = os.getcwd()
    python_path = sys.executable
    
    crontab_entries = [
        f"# BTC价格预测系统定时任务",
        f"# 每30分钟进行一次实时预测",
        f"*/30 * * * * cd {current_dir} && {python_path} real_time_predict.py >> logs/predict.log 2>&1",
        f"",
        f"# 每天凌晨2点进行反思学习",
        f"0 2 * * * cd {current_dir} && {python_path} daily_reflection.py >> logs/reflection.log 2>&1",
        f""
    ]
    
    crontab_content = "\n".join(crontab_entries)
    
    print("建议的crontab配置:")
    print("-" * 50)
    print(crontab_content)
    print("-" * 50)
    
    print("\n要设置定时任务，请执行以下步骤:")
    print("1. 运行命令: crontab -e")
    print("2. 将上述配置添加到crontab文件中")
    print("3. 保存并退出")
    
    # 保存到文件
    with open("crontab_config.txt", "w") as f:
        f.write(crontab_content)
    print("配置已保存到 crontab_config.txt 文件")

def test_scripts():
    """测试脚本运行"""
    print("\n测试脚本运行...")
    
    # 测试实时预测脚本
    print("测试实时预测脚本...")
    try:
        result = subprocess.run([sys.executable, "real_time_predict.py"], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✓ 实时预测脚本运行正常")
        else:
            print(f"✗ 实时预测脚本运行失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("✗ 实时预测脚本运行超时")
        return False
    except Exception as e:
        print(f"✗ 实时预测脚本测试失败: {e}")
        return False
    
    print("脚本测试通过！")
    return True

def main():
    """主函数"""
    print("=== BTC价格预测系统 - 生产环境部署检查 ===")
    print(f"时间: {datetime.now()}")
    print(f"工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.executable}")
    
    checks = [
        ("依赖包检查", check_dependencies),
        ("模型文件检查", check_model_files),
        ("API连接测试", test_api_connection),
        ("创建日志目录", create_log_directories),
        ("脚本运行测试", test_scripts)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        if not check_func():
            all_passed = False
            print(f"❌ {check_name} 失败")
        else:
            print(f"✅ {check_name} 通过")
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 所有检查通过！系统已准备好部署到生产环境")
        setup_crontab()
        
        print("\n下一步操作:")
        print("1. 设置定时任务 (参考上面的crontab配置)")
        print("2. 监控日志文件: logs/predict.log 和 logs/reflection.log")
        print("3. 定期检查模型性能")
        
    else:
        print("❌ 部分检查失败，请修复问题后重新运行")
    
    print(f"\n部署检查完成 - {datetime.now()}")

if __name__ == "__main__":
    main()
