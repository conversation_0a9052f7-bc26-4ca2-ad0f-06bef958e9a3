#!/usr/bin/env python3
"""
BTC实时价格预测脚本
功能：
1. 每30分钟从币安API获取最新BTC/USDT K线数据
2. 使用训练好的模型进行价格涨跌预测
3. 记录预测结果到日志文件，供反思学习使用

使用方法：
python real_time_predict.py

或设置crontab定时任务（建议每30分钟执行）：
*/30 * * * * cd /ai-model && python real_time_predict.py >> logs/predict.log 2>&1
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import warnings
import requests
import ta
from datetime import datetime, timedelta
import json

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置参数 ---
MODEL_DIR = "btc_reflection_model_v1"
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"

# 交易配置
SYMBOL = 'BTCUSDT'
INTERVAL = '30m'
KLINES_LIMIT = 150  # 获取足够的历史数据用于特征计算

# --- 核心函数 ---

def fetch_latest_klines_from_binance(symbol=SYMBOL, interval=INTERVAL, limit=KLINES_LIMIT):
    """
    从币安API获取最新的K线数据
    
    Args:
        symbol: 交易对符号
        interval: K线间隔
        limit: 获取的K线数量
    
    Returns:
        pandas.DataFrame: K线数据
    """
    try:
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        print(f"正在从币安API获取 {symbol} {interval} K线数据...")
        response = requests.get(BINANCE_API_URL, params=params, timeout=10)
        response.raise_for_status()

        data = response.json()

        # 转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # 数据类型转换
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'number_of_trades']:
            df[col] = df[col].astype(float)

        # 重命名和选择需要的列
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'number_of_trades']].copy()
        df.rename(columns={'timestamp': 'timestamp_utc'}, inplace=True)
        df.set_index('timestamp_utc', inplace=True)

        print(f"成功获取 {len(df)} 条K线数据，时间范围: {df.index[0]} 到 {df.index[-1]}")
        return df

    except Exception as e:
        print(f"获取币安API数据失败: {e}")
        return None

def create_features(df):
    """
    从原始K线数据创建特征（与训练时完全一致）
    
    Args:
        df: 原始K线数据
    
    Returns:
        pandas.DataFrame: 包含特征的数据
    """
    df_feat = df.copy()

    try:
        # 使用 ta 库计算技术指标，与训练数据保持一致
        # 动量指标
        df_feat['momentum_rsi'] = ta.momentum.RSIIndicator(df_feat['close']).rsi()

        # 趋势指标
        macd = ta.trend.MACD(df_feat['close'])
        df_feat['trend_macd'] = macd.macd()
        df_feat['trend_macd_signal'] = macd.macd_signal()
        df_feat['trend_macd_diff'] = macd.macd_diff()

        # 波动率指标 (布林带)
        bb = ta.volatility.BollingerBands(df_feat['close'])
        df_feat['volatility_bbm'] = bb.bollinger_mavg()
        df_feat['volatility_bbh'] = bb.bollinger_hband()
        df_feat['volatility_bbl'] = bb.bollinger_lband()
        df_feat['volatility_bbw'] = bb.bollinger_wband()
        df_feat['volatility_bbp'] = bb.bollinger_pband()

        # ADX 趋势强度指标
        adx = ta.trend.ADXIndicator(df_feat['high'], df_feat['low'], df_feat['close'])
        df_feat['trend_adx'] = adx.adx()
        df_feat['trend_adx_pos'] = adx.adx_pos()
        df_feat['trend_adx_neg'] = adx.adx_neg()

        # 成交量指标
        df_feat['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(df_feat['close'], df_feat['volume']).on_balance_volume()

        # 基础价格特征
        df_feat['price_change'] = df_feat['close'].pct_change()
        df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
        df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

        # 多时间框架特征
        windows = [3, 5, 10, 20, 50, 100]
        for window in windows:
            df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
            df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
            df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
            df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

        # 额外的技术指标（与训练脚本一致）
        delta = df_feat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-9)
        df_feat['rsi_14'] = 100 - (100 / (1 + rs))

        ema_fast = df_feat['close'].ewm(span=12).mean()
        ema_slow = df_feat['close'].ewm(span=26).mean()
        df_feat['macd'] = ema_fast - ema_slow
        df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
        df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

        df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
        df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
        df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

        # 清理数据
        df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
        df_feat.dropna(inplace=True)

        return df_feat

    except Exception as e:
        print(f"特征工程过程出错: {e}")
        return pd.DataFrame()

def load_model_and_preprocessors():
    """
    加载训练好的模型和预处理器
    
    Returns:
        tuple: (model, scaler, feature_columns)
    """
    try:
        model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
        scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
        feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))
        
        print("成功加载模型和预处理器")
        return model, scaler, feature_columns
        
    except FileNotFoundError as e:
        print(f"错误：找不到模型文件 - {e}")
        print("请先运行 train_initial_model.py 训练初始模型")
        return None, None, None
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None, None, None

def make_prediction(klines_data, model, scaler, feature_columns):
    """
    对最新数据进行预测
    
    Args:
        klines_data: K线数据
        model: 训练好的模型
        scaler: 特征缩放器
        feature_columns: 特征列名列表
    
    Returns:
        dict: 预测结果
    """
    try:
        # 1. 特征工程
        df_featured = create_features(klines_data)
        
        if df_featured.empty:
            print("特征工程后没有可用数据")
            return None

        # 2. 获取最新的一条数据进行预测
        latest_data = df_featured.iloc[-1:].copy()
        
        # 确保所有需要的特征列都存在
        missing_features = set(feature_columns) - set(latest_data.columns)
        if missing_features:
            print(f"缺少特征列: {missing_features}")
            return None

        # 3. 准备预测数据
        X_latest = latest_data[feature_columns]
        X_latest_scaled = scaler.transform(X_latest)
        
        # 4. 进行预测
        prediction = model.predict(X_latest_scaled)[0]
        prediction_proba = model.predict_proba(X_latest_scaled)[0]
        
        # 5. 准备预测结果
        result = {
            'timestamp': latest_data.index[0],
            'close_price': latest_data['close'].iloc[0],
            'prediction': int(prediction),
            'prediction_proba_down': float(prediction_proba[0]),
            'prediction_proba_up': float(prediction_proba[1]),
            'prediction_confidence': float(max(prediction_proba)),
            'features': X_latest.iloc[0].to_dict()
        }
        
        return result
        
    except Exception as e:
        print(f"预测过程出错: {e}")
        return None

def save_prediction_log(prediction_result):
    """
    保存预测结果到日志文件
    
    Args:
        prediction_result: 预测结果字典
    """
    try:
        # 准备日志数据
        log_data = {
            'timestamp_utc': prediction_result['timestamp'],
            'close_price': prediction_result['close_price'],
            'prediction': prediction_result['prediction'],
            'prediction_proba_up': prediction_result['prediction_proba_up'],
            'prediction_confidence': prediction_result['prediction_confidence']
        }
        
        # 添加特征数据
        log_data.update(prediction_result['features'])
        
        # 转换为DataFrame
        log_df = pd.DataFrame([log_data])
        log_df.set_index('timestamp_utc', inplace=True)
        
        # 保存到文件
        if os.path.exists(PREDICTION_LOG_PATH):
            log_df.to_csv(PREDICTION_LOG_PATH, mode='a', header=False)
        else:
            log_df.to_csv(PREDICTION_LOG_PATH)
        
        print(f"预测结果已保存到 {PREDICTION_LOG_PATH}")
        
    except Exception as e:
        print(f"保存预测日志失败: {e}")

def main():
    """
    主函数：执行实时预测流程
    """
    print(f"\n=== BTC实时价格预测 - {datetime.now()} ===")
    
    # 1. 加载模型和预处理器
    model, scaler, feature_columns = load_model_and_preprocessors()
    if model is None:
        print("无法加载模型，退出程序")
        return
    
    # 2. 获取最新市场数据
    klines_data = fetch_latest_klines_from_binance()
    if klines_data is None:
        print("无法获取市场数据，退出程序")
        return
    
    # 3. 进行预测
    prediction_result = make_prediction(klines_data, model, scaler, feature_columns)
    if prediction_result is None:
        print("预测失败，退出程序")
        return
    
    # 4. 显示预测结果
    print(f"\n--- 预测结果 ---")
    print(f"时间: {prediction_result['timestamp']}")
    print(f"当前价格: ${prediction_result['close_price']:.2f}")
    print(f"预测方向: {'上涨' if prediction_result['prediction'] == 1 else '下跌/横盘'}")
    print(f"上涨概率: {prediction_result['prediction_proba_up']:.2%}")
    print(f"预测置信度: {prediction_result['prediction_confidence']:.2%}")
    
    # 5. 保存预测日志
    save_prediction_log(prediction_result)
    
    print(f"\n预测完成 - {datetime.now()}")

if __name__ == "__main__":
    main()
