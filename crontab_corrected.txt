# BTC价格预测系统 - 修正版定时任务配置
# 
# 重要说明：
# 1. 请将 /ai-model 替换为您的实际工作目录路径
# 2. 请将 python 替换为您的实际Python路径（如果需要）
# 3. 确保logs目录存在：mkdir -p /ai-model/logs
#
# 当前系统信息：
# - 工作目录：/ai-model
# - Python环境：ml_env (conda环境)
# - 建议Python路径：/home/<USER>/miniconda3/envs/ml_env/bin/python

# ============================================================================
# 方式1：持续运行模式（推荐）
# ============================================================================

# 每天凌晨2点进行反思学习（分析预测结果，优化模型）
0 2 * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控（检查系统状态和性能）
0 * * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python monitor_system.py >> logs/monitor.log 2>&1

# 每周日凌晨3点清理旧日志文件（可选）
0 3 * * 0 cd /ai-model && find logs/ -name "*.log" -mtime +30 -delete

# 系统重启时自动启动持续预测脚本（可选）
@reboot cd /ai-model && nohup /home/<USER>/miniconda3/envs/ml_env/bin/python continuous_predict.py > logs/continuous_predict.log 2>&1 &

# ============================================================================
# 方式2：传统定时任务模式（如果不使用持续运行）
# ============================================================================

# 如果您不想使用持续运行模式，可以启用以下配置：
# 注意：启用方式2时，请注释掉方式1的@reboot行

# 每30分钟进行一次BTC价格预测
# */30 * * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python real_time_predict.py >> logs/predict.log 2>&1

# 每天凌晨2点进行反思学习
# 0 2 * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控
# 0 * * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python monitor_system.py >> logs/monitor.log 2>&1

# 每周日凌晨3点清理旧日志文件
# 0 3 * * 0 cd /ai-model && find logs/ -name "*.log" -mtime +30 -delete

# ============================================================================
# 设置步骤
# ============================================================================

# 1. 确保logs目录存在：
#    mkdir -p /ai-model/logs

# 2. 测试Python路径是否正确：
#    /home/<USER>/miniconda3/envs/ml_env/bin/python --version

# 3. 测试脚本是否可以运行：
#    cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python daily_reflection.py

# 4. 编辑crontab：
#    crontab -e

# 5. 复制上述配置（选择方式1或方式2）到crontab文件中

# 6. 保存并退出（在vi/vim中按ESC，然后输入:wq）

# 7. 验证crontab设置：
#    crontab -l

# 8. 查看cron服务状态：
#    systemctl status cron
#    # 或者
#    service cron status

# ============================================================================
# 故障排除
# ============================================================================

# 如果定时任务不执行，检查以下几点：

# 1. 检查cron服务是否运行：
#    systemctl status cron

# 2. 检查cron日志：
#    tail -f /var/log/cron
#    # 或者
#    tail -f /var/log/syslog | grep CRON

# 3. 检查脚本权限：
#    ls -la /ai-model/*.py

# 4. 手动测试命令：
#    cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python daily_reflection.py

# 5. 检查环境变量（cron环境可能不同）：
#    在脚本开头添加：
#    export PATH=/home/<USER>/miniconda3/envs/ml_env/bin:$PATH

# ============================================================================
# 监控命令
# ============================================================================

# 查看当前crontab设置：
# crontab -l

# 查看反思学习日志：
# tail -f /ai-model/logs/reflection.log

# 查看监控日志：
# tail -f /ai-model/logs/monitor.log

# 查看持续预测日志（方式1）：
# tail -f /ai-model/logs/continuous_predict.log

# 检查持续预测进程状态：
# ps aux | grep continuous_predict.py

# 停止持续预测进程：
# pkill -f continuous_predict.py

# 手动启动持续预测进程：
# cd /ai-model && nohup /home/<USER>/miniconda3/envs/ml_env/bin/python continuous_predict.py > logs/continuous_predict.log 2>&1 &

# 检查cron日志：
# tail -f /var/log/cron

# 重启cron服务（如果需要）：
# sudo systemctl restart cron
