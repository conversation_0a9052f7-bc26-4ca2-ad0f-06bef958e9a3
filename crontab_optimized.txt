# BTC价格预测系统 - 优化版定时任务配置
# 生成时间: 2025-07-14 08:23:38.602549
# 工作目录: /ai-model
# Python路径: /home/<USER>/miniconda3/envs/ml_env/bin/python

# 每30分钟进行一次BTC价格预测
*/30 * * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python real_time_predict.py >> logs/predict.log 2>&1

# 每天凌晨2点进行反思学习
0 2 * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控
0 * * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python monitor_system.py >> logs/monitor.log 2>&1

# 每周日凌晨3点清理旧日志文件
0 3 * * 0 cd /ai-model && find logs/ -name "*.log" -mtime +30 -delete
