#!/usr/bin/env python3
"""
演示持续预测脚本
模拟30分钟间隔的预测和验证过程
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import os
from continuous_predict import *
import warnings
warnings.filterwarnings('ignore')

def simulate_time_series_predictions():
    """模拟时间序列预测，演示验证功能"""
    print("🚀 开始演示持续预测和验证功能")
    print("=" * 60)
    
    # 清理旧的预测日志
    if os.path.exists(PREDICTION_LOG_PATH):
        os.remove(PREDICTION_LOG_PATH)
        print("🗑️ 已清理旧的预测日志")
    
    # 加载模型
    if not load_model_and_preprocessors():
        print("❌ 模型加载失败")
        return
    
    print("✅ 模型加载成功")
    
    # 模拟多次预测
    for i in range(5):
        print(f"\n{'='*20} 第 {i+1} 次预测 {'='*20}")
        
        # 获取当前数据
        klines_data = fetch_latest_klines_from_binance()
        if klines_data is None:
            print("❌ 无法获取市场数据")
            continue
        
        # 为了演示验证功能，我们人工调整时间戳
        # 模拟每次预测间隔30分钟
        if i > 0:
            # 调整最新数据的时间戳，模拟30分钟后
            new_timestamp = klines_data.index[-1] + timedelta(minutes=30 * i)
            klines_data.index = klines_data.index[:-1].tolist() + [new_timestamp]
            
            # 稍微调整价格，模拟价格变化
            price_change = np.random.normal(0, 0.002)  # 0.2%的随机变化
            klines_data.loc[new_timestamp, 'close'] *= (1 + price_change)
            klines_data.loc[new_timestamp, 'high'] = max(klines_data.loc[new_timestamp, 'high'], klines_data.loc[new_timestamp, 'close'])
            klines_data.loc[new_timestamp, 'low'] = min(klines_data.loc[new_timestamp, 'low'], klines_data.loc[new_timestamp, 'close'])
        
        # 执行预测
        success = run_single_prediction()
        
        if not success:
            print("❌ 预测失败")
            continue
        
        # 等待一下，模拟时间流逝
        time.sleep(2)
    
    print(f"\n{'='*60}")
    print("📊 最终预测准确率统计")
    print("=" * 60)
    
    # 显示最终统计
    accuracy_stats = calculate_current_accuracy()
    if accuracy_stats:
        print(f"总预测次数: {accuracy_stats['total_predictions']}")
        print(f"已验证预测: {accuracy_stats['verified_predictions']}")
        print(f"正确预测次数: {accuracy_stats['correct_predictions']}")
        print(f"整体准确率: {accuracy_stats['overall_accuracy']:.2%}")
        
        if accuracy_stats['up_predictions_count'] > 0:
            print(f"上涨预测: {accuracy_stats['up_predictions_count']} 次, 准确率: {accuracy_stats['up_accuracy']:.2%}")
        if accuracy_stats['down_predictions_count'] > 0:
            print(f"下跌预测: {accuracy_stats['down_predictions_count']} 次, 准确率: {accuracy_stats['down_accuracy']:.2%}")
    
    # 显示详细的预测日志
    print(f"\n📋 详细预测日志:")
    if os.path.exists(PREDICTION_LOG_PATH):
        df = pd.read_csv(PREDICTION_LOG_PATH)
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
        
        print(f"{'时间':<20} {'价格':<10} {'预测':<6} {'实际':<6} {'正确':<6} {'概率':<8}")
        print("-" * 60)
        
        for _, row in df.iterrows():
            timestamp = row['timestamp_utc'].strftime('%m-%d %H:%M')
            price = f"${row['close_price']:.0f}"
            pred_dir = "上涨" if row['prediction'] == 1 else "下跌"
            
            if pd.notna(row['actual_direction']):
                actual_dir = "上涨" if row['actual_direction'] == 1 else "下跌"
                is_correct = "✅" if row['is_correct'] else "❌"
            else:
                actual_dir = "待验证"
                is_correct = "⏳"
            
            prob = f"{row['prediction_proba_up']:.2%}"
            
            print(f"{timestamp:<20} {price:<10} {pred_dir:<6} {actual_dir:<6} {is_correct:<6} {prob:<8}")
    
    print(f"\n✅ 演示完成！")

def run_real_continuous_prediction():
    """运行真实的持续预测（每30分钟一次）"""
    print("🚀 启动真实持续预测系统")
    print("⏰ 每30分钟自动预测一次")
    print("🔍 自动验证上次预测准确性")
    print("📊 实时统计预测准确率")
    print("按 Ctrl+C 停止运行\n")
    
    # 加载模型
    if not load_model_and_preprocessors():
        print("❌ 模型加载失败")
        return
    
    prediction_count = 0
    
    try:
        while True:
            prediction_count += 1
            print(f"\n{'='*20} 第 {prediction_count} 次预测 {'='*20}")
            
            # 执行预测
            success = run_single_prediction()
            
            if success:
                print(f"📊 累计预测次数: {prediction_count}")
                
                # 计算下次预测时间
                next_prediction_time = datetime.now() + timedelta(minutes=30)
                print(f"⏰ 下次预测时间: {next_prediction_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 等待30分钟
                print(f"⏳ 等待30分钟...")
                time.sleep(30 * 60)  # 30分钟 = 1800秒
            else:
                print("❌ 预测失败，5分钟后重试...")
                time.sleep(5 * 60)  # 5分钟后重试
                
    except KeyboardInterrupt:
        print(f"\n\n🛑 用户停止运行")
        print(f"📊 总共完成 {prediction_count} 次预测")
        
        # 显示最终统计
        accuracy_stats = calculate_current_accuracy()
        if accuracy_stats:
            print(f"\n📊 最终统计:")
            print(f"   总预测次数: {accuracy_stats['total_predictions']}")
            print(f"   已验证预测: {accuracy_stats['verified_predictions']}")
            print(f"   整体准确率: {accuracy_stats['overall_accuracy']:.2%}")
        
        print(f"✅ 程序已安全退出")

def main():
    """主函数"""
    print("BTC持续预测系统")
    print("1. 演示模式（快速模拟5次预测）")
    print("2. 真实模式（每30分钟预测一次）")
    
    choice = input("\n请选择模式 (1/2): ").strip()
    
    if choice == "1":
        simulate_time_series_predictions()
    elif choice == "2":
        run_real_continuous_prediction()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
