#!/usr/bin/env python3
"""
预测准确率分析脚本
用于分析历史预测的准确率
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import requests

# 配置
PREDICTION_LOG_PATH = "btc_reflection_model_v1/prediction_log_clean.csv"
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"

def fetch_historical_prices(start_time, end_time, symbol='BTCUSDT', interval='30m'):
    """获取历史价格数据用于验证预测准确率"""
    try:
        # 转换时间为毫秒时间戳
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
        
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': start_ts,
            'endTime': end_ts,
            'limit': 1000
        }
        
        response = requests.get(BINANCE_API_URL, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if not data:
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])
        
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['close'] = df['close'].astype(float)
        df.set_index('timestamp', inplace=True)
        
        return df[['close']]
        
    except Exception as e:
        print(f"获取历史价格失败: {e}")
        return None

def calculate_detailed_accuracy():
    """计算详细的预测准确率"""
    try:
        if not os.path.exists(PREDICTION_LOG_PATH):
            print("❌ 预测日志文件不存在")
            return None
        
        # 读取预测日志
        df = pd.read_csv(PREDICTION_LOG_PATH)
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
        df.set_index('timestamp_utc', inplace=True)
        df.sort_index(inplace=True)
        
        if len(df) < 2:
            print("❌ 预测数据不足，需要至少2条记录")
            return None
        
        print(f"📊 分析 {len(df)} 条预测记录...")
        
        # 获取历史价格数据来验证预测
        start_time = df.index[0]
        end_time = df.index[-1] + timedelta(hours=1)  # 多获取一小时的数据
        
        historical_prices = fetch_historical_prices(start_time, end_time)
        if historical_prices is None:
            print("❌ 无法获取历史价格数据")
            return None
        
        # 合并预测数据和历史价格
        df_merged = df.join(historical_prices, how='left', rsuffix='_historical')
        
        # 计算下一个时间点的价格（30分钟后）
        df_merged['next_price'] = df_merged['close'].shift(-1)
        
        # 如果没有下一个价格，尝试从历史数据中获取
        for i in range(len(df_merged)):
            if pd.isna(df_merged['next_price'].iloc[i]):
                current_time = df_merged.index[i]
                next_time = current_time + timedelta(minutes=30)
                
                # 在历史价格中查找最接近的时间点
                closest_time = historical_prices.index[historical_prices.index >= next_time]
                if len(closest_time) > 0:
                    df_merged.loc[current_time, 'next_price'] = historical_prices.loc[closest_time[0], 'close']
        
        # 计算实际收益率
        df_merged['actual_return'] = (df_merged['next_price'] - df_merged['close_price']) / df_merged['close_price']
        
        # 使用动态阈值判断实际涨跌
        returns = df_merged['actual_return'].dropna()
        if len(returns) > 10:
            threshold = returns.rolling(window=min(50, len(returns)), min_periods=10).std().mean() * 0.5
        else:
            threshold = returns.std() * 0.5
            
        if pd.isna(threshold) or threshold == 0:
            threshold = 0.005  # 0.5%的固定阈值
        
        print(f"📏 使用阈值: ±{threshold:.4f} ({threshold:.2%})")
        
        # 判断实际方向
        df_merged['actual_direction'] = 0  # 默认为下跌/横盘
        df_merged.loc[df_merged['actual_return'] > threshold, 'actual_direction'] = 1  # 上涨
        
        # 计算准确率（排除没有下一个价格的记录）
        valid_predictions = df_merged.dropna(subset=['next_price', 'actual_return']).copy()
        
        if len(valid_predictions) == 0:
            print("❌ 没有有效的预测记录用于计算准确率")
            return None
        
        print(f"✅ 有效预测记录: {len(valid_predictions)} 条")
        
        # 整体准确率
        correct_predictions = (valid_predictions['prediction'] == valid_predictions['actual_direction']).sum()
        total_predictions = len(valid_predictions)
        overall_accuracy = correct_predictions / total_predictions
        
        # 分类准确率
        up_predictions = valid_predictions[valid_predictions['prediction'] == 1]
        down_predictions = valid_predictions[valid_predictions['prediction'] == 0]
        
        up_accuracy = 0
        down_accuracy = 0
        up_correct = 0
        down_correct = 0
        
        if len(up_predictions) > 0:
            up_correct = (up_predictions['prediction'] == up_predictions['actual_direction']).sum()
            up_accuracy = up_correct / len(up_predictions)
        
        if len(down_predictions) > 0:
            down_correct = (down_predictions['prediction'] == down_predictions['actual_direction']).sum()
            down_accuracy = down_correct / len(down_predictions)
        
        # 实际市场方向分布
        actual_up = (valid_predictions['actual_direction'] == 1).sum()
        actual_down = (valid_predictions['actual_direction'] == 0).sum()
        
        # 预测分布
        pred_up = len(up_predictions)
        pred_down = len(down_predictions)
        
        # 混淆矩阵
        tp = ((valid_predictions['prediction'] == 1) & (valid_predictions['actual_direction'] == 1)).sum()  # 预测上涨，实际上涨
        tn = ((valid_predictions['prediction'] == 0) & (valid_predictions['actual_direction'] == 0)).sum()  # 预测下跌，实际下跌
        fp = ((valid_predictions['prediction'] == 1) & (valid_predictions['actual_direction'] == 0)).sum()  # 预测上涨，实际下跌
        fn = ((valid_predictions['prediction'] == 0) & (valid_predictions['actual_direction'] == 1)).sum()  # 预测下跌，实际上涨
        
        # 计算精确率、召回率、F1分数
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
        
        results = {
            'total_predictions': total_predictions,
            'correct_predictions': correct_predictions,
            'overall_accuracy': overall_accuracy,
            'threshold_used': threshold,
            
            # 预测分布
            'predicted_up_count': pred_up,
            'predicted_down_count': pred_down,
            'predicted_up_ratio': pred_up / total_predictions,
            
            # 实际分布
            'actual_up_count': actual_up,
            'actual_down_count': actual_down,
            'actual_up_ratio': actual_up / total_predictions,
            
            # 分类准确率
            'up_accuracy': up_accuracy,
            'down_accuracy': down_accuracy,
            'up_correct': up_correct,
            'down_correct': down_correct,
            
            # 混淆矩阵
            'true_positive': tp,
            'true_negative': tn,
            'false_positive': fp,
            'false_negative': fn,
            
            # 性能指标
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            
            # 详细数据
            'valid_data': valid_predictions
        }
        
        return results
        
    except Exception as e:
        print(f"❌ 计算准确率失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_accuracy_report(results):
    """打印详细的准确率报告"""
    if results is None:
        return
    
    print("\n" + "="*60)
    print("📊 BTC价格预测准确率分析报告")
    print("="*60)
    
    print(f"\n📈 基本统计:")
    print(f"   总预测次数: {results['total_predictions']}")
    print(f"   正确预测次数: {results['correct_predictions']}")
    print(f"   整体准确率: {results['overall_accuracy']:.2%}")
    print(f"   使用阈值: ±{results['threshold_used']:.4f} ({results['threshold_used']:.2%})")
    
    print(f"\n🎯 预测分布:")
    print(f"   预测上涨次数: {results['predicted_up_count']} ({results['predicted_up_ratio']:.1%})")
    print(f"   预测下跌次数: {results['predicted_down_count']} ({1-results['predicted_up_ratio']:.1%})")
    
    print(f"\n📊 实际市场分布:")
    print(f"   实际上涨次数: {results['actual_up_count']} ({results['actual_up_ratio']:.1%})")
    print(f"   实际下跌次数: {results['actual_down_count']} ({1-results['actual_up_ratio']:.1%})")
    
    print(f"\n🎯 分类准确率:")
    if results['predicted_up_count'] > 0:
        print(f"   上涨预测准确率: {results['up_accuracy']:.2%} ({results['up_correct']}/{results['predicted_up_count']})")
    if results['predicted_down_count'] > 0:
        print(f"   下跌预测准确率: {results['down_accuracy']:.2%} ({results['down_correct']}/{results['predicted_down_count']})")
    
    print(f"\n📋 混淆矩阵:")
    print(f"                    实际")
    print(f"                上涨  下跌")
    print(f"   预测  上涨    {results['true_positive']:3d}   {results['false_positive']:3d}")
    print(f"         下跌    {results['false_negative']:3d}   {results['true_negative']:3d}")
    
    print(f"\n📊 性能指标:")
    print(f"   精确率 (Precision): {results['precision']:.2%}")
    print(f"   召回率 (Recall): {results['recall']:.2%}")
    print(f"   F1分数: {results['f1_score']:.2%}")
    
    # 显示最近几次预测的详细情况
    print(f"\n🔍 最近预测详情:")
    recent_data = results['valid_data'].tail(10)
    for idx, row in recent_data.iterrows():
        actual_dir = "上涨" if row['actual_direction'] == 1 else "下跌"
        pred_dir = "上涨" if row['prediction'] == 1 else "下跌"
        correct = "✅" if row['prediction'] == row['actual_direction'] else "❌"
        
        print(f"   {idx.strftime('%m-%d %H:%M')} | 预测:{pred_dir} | 实际:{actual_dir} | 收益:{row['actual_return']:+.3%} | {correct}")

def main():
    """主函数"""
    print("🚀 BTC预测准确率分析器启动")
    
    results = calculate_detailed_accuracy()
    print_accuracy_report(results)
    
    print("\n✅ 分析完成")

if __name__ == "__main__":
    main()
