#!/usr/bin/env python3
"""
测试修复后的预测验证逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def simulate_verification():
    """模拟你遇到的验证问题"""
    
    print("🧪 模拟预测验证问题")
    print("=" * 50)
    
    # 你的实际例子
    cases = [
        {
            "prediction_time": "07-16 02:30",
            "previous_price": 117291.93,
            "current_price": 117389.11,
            "predicted_direction": 0,  # 预测下跌
            "description": "第一次预测验证"
        },
        {
            "prediction_time": "07-16 03:00", 
            "previous_price": 117389.11,
            "current_price": 117456.98,
            "predicted_direction": 0,  # 预测下跌
            "description": "第二次预测验证"
        }
    ]
    
    # 使用训练时的正确阈值
    threshold = 0.002352  # 0.2352%
    
    print(f"📏 使用训练时的阈值: ±{threshold:.6f} ({threshold:.4%})")
    print()
    
    for i, case in enumerate(cases, 1):
        print(f"📊 {case['description']} (第{i}次):")
        print(f"   预测时间: {case['prediction_time']}")
        print(f"   价格变化: ${case['previous_price']:,.2f} → ${case['current_price']:,.2f}")
        
        # 计算实际价格变化
        price_change = (case['current_price'] - case['previous_price']) / case['previous_price']
        print(f"   价格变化率: {price_change:+.6f} ({price_change:+.4%})")
        
        # 判断实际方向（修复后的逻辑）
        if price_change > threshold:
            actual_direction = 1  # 明确上涨
            direction_text = "🔺 上涨"
        elif price_change < -threshold:
            actual_direction = 0  # 明确下跌
            direction_text = "🔻 下跌"
        else:
            # 价格变化在阈值范围内，认为是横盘，归类为下跌/横盘（0）
            actual_direction = 0
            if price_change > 0:
                direction_text = "🔻 横盘（小幅上涨）"
            else:
                direction_text = "🔻 横盘（小幅下跌）"
        
        # 判断预测是否正确
        is_correct = (case['predicted_direction'] == actual_direction)
        
        print(f"   预测方向: {'🔺 上涨' if case['predicted_direction'] == 1 else '🔻 下跌'}")
        print(f"   实际方向: {direction_text}")
        print(f"   预测结果: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        # 解释
        if price_change > 0 and actual_direction == 0:
            print(f"   💡 解释: 虽然价格上涨了{price_change:.4%}，但小于阈值{threshold:.4%}，")
            print(f"           按照模型定义被归类为'横盘/非明确上涨'（类别0）")
        
        print()

if __name__ == "__main__":
    simulate_verification()
