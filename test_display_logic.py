#!/usr/bin/env python3
"""
测试修复后的显示逻辑
"""

def test_display_logic():
    """测试显示逻辑"""
    
    print("🧪 测试修复后的显示逻辑")
    print("=" * 50)
    
    # 你的实际例子
    test_cases = [
        {
            "name": "你的例子1",
            "price_change": 0.00111,  # +0.111%
            "predicted_direction": 0,  # 预测下跌
            "description": "小幅上涨但预测下跌"
        },
        {
            "name": "你的例子2", 
            "price_change": 0.00174,  # +0.174%
            "predicted_direction": 0,  # 预测下跌
            "description": "小幅上涨但预测下跌"
        },
        {
            "name": "你的例子3",
            "price_change": 0.00021,  # +0.021%
            "predicted_direction": 0,  # 预测下跌
            "description": "微幅上涨但预测下跌"
        },
        {
            "name": "明确上涨",
            "price_change": 0.003,    # +0.3%
            "predicted_direction": 1,  # 预测上涨
            "description": "明确上涨且预测正确"
        },
        {
            "name": "明确下跌",
            "price_change": -0.003,   # -0.3%
            "predicted_direction": 0,  # 预测下跌
            "description": "明确下跌且预测正确"
        }
    ]
    
    threshold = 0.002352  # 0.2352%
    
    print(f"📏 使用阈值: ±{threshold:.6f} ({threshold:.4%})")
    print()
    
    for case in test_cases:
        price_change = case["price_change"]
        predicted_direction = case["predicted_direction"]
        
        # 判断实际方向
        if price_change > threshold:
            actual_direction = 1  # 明确上涨
        elif price_change < -threshold:
            actual_direction = 0  # 明确下跌
        else:
            actual_direction = 0  # 横盘
        
        # 判断预测是否正确
        is_correct = (predicted_direction == actual_direction)
        
        # 显示逻辑（修复后）
        if actual_direction == 1:
            actual_direction_text = "🔺 上涨"
        else:
            # actual_direction = 0 的情况需要细分
            if price_change > 0:
                if price_change > threshold:
                    actual_direction_text = "🔺 上涨"  # 这种情况不应该发生
                else:
                    actual_direction_text = f"🔻 横盘(小幅上涨+{price_change:.3%})"
            elif price_change < 0:
                if price_change < -threshold:
                    actual_direction_text = "🔻 下跌"
                else:
                    actual_direction_text = f"🔻 横盘(小幅下跌{price_change:.3%})"
            else:
                actual_direction_text = "🔻 横盘(无变化)"
        
        print(f"📊 {case['name']} - {case['description']}:")
        print(f"   价格变化: {price_change:+.6f} ({price_change:+.4%})")
        print(f"   预测方向: {'🔺 上涨' if predicted_direction == 1 else '🔻 下跌/横盘'}")
        print(f"   实际方向: {actual_direction_text}")
        print(f"   预测结果: {'✅ 正确' if is_correct else '❌ 错误'}")
        print()

if __name__ == "__main__":
    test_display_logic()
