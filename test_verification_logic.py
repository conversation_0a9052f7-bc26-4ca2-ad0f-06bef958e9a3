#!/usr/bin/env python3
"""
测试预测验证逻辑的修复
"""

import pandas as pd
import numpy as np

def test_verification_logic():
    """测试验证逻辑"""
    
    print("🧪 测试预测验证逻辑")
    print("=" * 50)
    
    # 测试案例
    test_cases = [
        {
            "name": "你的例子1",
            "previous_price": 117291.93,
            "current_price": 117389.11,
            "expected_direction": "上涨（但可能被归类为横盘）"
        },
        {
            "name": "你的例子2", 
            "previous_price": 117389.11,
            "current_price": 117456.98,
            "expected_direction": "上涨（但可能被归类为横盘）"
        },
        {
            "name": "明确上涨",
            "previous_price": 100000,
            "current_price": 100300,  # +0.3%
            "expected_direction": "明确上涨"
        },
        {
            "name": "明确下跌",
            "previous_price": 100000,
            "current_price": 99700,   # -0.3%
            "expected_direction": "明确下跌"
        },
        {
            "name": "小幅上涨",
            "previous_price": 100000,
            "current_price": 100100,  # +0.1%
            "expected_direction": "横盘（小幅上涨）"
        },
        {
            "name": "小幅下跌",
            "previous_price": 100000,
            "current_price": 99900,   # -0.1%
            "expected_direction": "横盘（小幅下跌）"
        }
    ]
    
    # 训练时的阈值
    threshold = 0.002352  # 0.2352%
    
    print(f"📏 使用阈值: ±{threshold:.6f} ({threshold:.4%})")
    print()
    
    for case in test_cases:
        previous_price = case["previous_price"]
        current_price = case["current_price"]
        
        # 计算价格变化
        price_change = (current_price - previous_price) / previous_price
        
        # 判断实际方向（修复后的逻辑）
        if price_change > threshold:
            actual_direction = 1  # 明确上涨
            direction_text = "🔺 上涨"
        elif price_change < -threshold:
            actual_direction = 0  # 明确下跌  
            direction_text = "🔻 下跌"
        else:
            # 价格变化在阈值范围内，认为是横盘，归类为下跌/横盘（0）
            actual_direction = 0
            if price_change > 0:
                direction_text = "🔻 横盘（小幅上涨）"
            else:
                direction_text = "🔻 横盘（小幅下跌）"
        
        print(f"📊 {case['name']}:")
        print(f"   价格: ${previous_price:,.2f} → ${current_price:,.2f}")
        print(f"   变化: {price_change:+.6f} ({price_change:+.4%})")
        print(f"   判断: {direction_text}")
        print(f"   预期: {case['expected_direction']}")
        print()

if __name__ == "__main__":
    test_verification_logic()
