# BTC价格预测系统 - 生产环境部署指南

## 📋 系统概述

这是一个基于机器学习的BTC价格预测系统，具备自动反思学习能力。系统包含两个核心组件：

1. **实时预测组件** (`real_time_predict.py`) - 每30分钟获取最新数据并进行预测
2. **反思学习组件** (`daily_reflection.py`) - 每日验证预测结果并优化模型

## 🚀 快速部署

### 1. 环境准备

确保您已经安装了所需的依赖包：

```bash
pip install pandas numpy lightgbm joblib requests scikit-learn
```

### 2. 训练初始模型

如果还没有训练初始模型，请先运行：

```bash
python train_initial_model.py
```

### 3. 部署检查

运行部署检查脚本，确保所有组件正常：

```bash
python setup_production.py
```

### 4. 设置定时任务

编辑crontab：

```bash
crontab -e
```

添加以下配置：

```bash
# BTC价格预测系统定时任务
# 每30分钟进行一次实时预测
*/30 * * * * cd /ai-model && python real_time_predict.py >> logs/predict.log 2>&1

# 每天凌晨2点进行反思学习
0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1
```

## 📁 文件结构

```
/ai-model/
├── real_time_predict.py          # 实时预测脚本
├── daily_reflection.py           # 反思学习脚本
├── setup_production.py           # 部署检查脚本
├── train_initial_model.py        # 初始模型训练
├── btc_reflection_model_v1/       # 模型文件目录
│   ├── lgbm_model.joblib         # 训练好的模型
│   ├── scaler.joblib             # 特征缩放器
│   ├── feature_columns.joblib    # 特征列名
│   ├── prediction_log.csv        # 预测日志
│   └── reflection_data.csv       # 反思数据存档
└── logs/                         # 日志目录
    ├── predict.log               # 预测日志
    └── reflection.log            # 反思学习日志
```

## 🔄 工作流程

### 实时预测流程

1. **数据获取**: 从币安API获取最新的BTC/USDT 30分钟K线数据
2. **特征工程**: 计算技术指标和特征
3. **模型预测**: 使用训练好的模型进行预测
4. **结果记录**: 将预测结果记录到日志文件

### 反思学习流程

1. **数据验证**: 获取历史数据，验证之前的预测结果
2. **准确率评估**: 计算预测准确率，识别错误样本
3. **增量学习**: 如果准确率低于阈值，使用错误样本进行增量训练
4. **模型更新**: 保存更新后的模型
5. **日志清理**: 清理预测日志，为下一轮做准备

## 📊 监控和维护

### 日志监控

实时查看预测日志：
```bash
tail -f logs/predict.log
```

实时查看反思学习日志：
```bash
tail -f logs/reflection.log
```

### 性能监控

检查预测准确率：
```bash
# 查看最近的反思学习结果
grep "准确率" logs/reflection.log | tail -5
```

检查模型更新情况：
```bash
# 查看模型更新记录
ls -la btc_reflection_model_v1/lgbm_model_iter_*.joblib
```

### 手动执行

如需手动执行预测：
```bash
python real_time_predict.py
```

如需手动执行反思学习：
```bash
python daily_reflection.py
```

## ⚙️ 配置参数

### 实时预测配置

在 `real_time_predict.py` 中可以调整：

- `symbol`: 交易对（默认：BTCUSDT）
- `interval`: K线间隔（默认：30m）
- `limit`: 获取的历史K线数量（默认：150）

### 反思学习配置

在 `daily_reflection.py` 中可以调整：

- `days_back`: 获取历史数据的天数（默认：7天）
- `min_error_samples`: 最少错误样本数量（默认：10）
- `accuracy_threshold`: 触发重训练的准确率阈值（默认：60%）

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 确认币安API可访问
   - 检查防火墙设置

2. **模型加载失败**
   - 确认模型文件存在
   - 重新训练初始模型
   - 检查文件权限

3. **预测结果异常**
   - 检查输入数据质量
   - 验证特征工程逻辑
   - 查看错误日志

### 日志分析

预测日志格式：
```
=== BTC价格预测 - 2024-01-01 12:30:00 ===
正在从币安API获取 BTCUSDT 30m K线数据...
成功获取 150 条K线数据
--- 预测结果 ---
时间: 2024-01-01 12:30:00
当前价格: $45,123.45
预测方向: 上涨
上涨概率: 65.23%
预测完成！
```

反思学习日志格式：
```
=== 每日反思学习 - 2024-01-01 02:00:00 ===
成功加载 48 条预测记录
验证了 45 条预测
正确预测: 32 条
准确率: 71.11%
错误样本: 13 条
准确率(71.11%)良好，暂不重训练
反思学习完成！
```

## 📈 性能优化建议

1. **定期备份模型**: 保存表现良好的模型版本
2. **监控准确率趋势**: 设置准确率告警
3. **调整学习频率**: 根据市场变化调整反思学习频率
4. **特征工程优化**: 定期评估和优化特征
5. **参数调优**: 根据实际表现调整模型参数

## 🔒 安全注意事项

1. **API密钥管理**: 如需要交易功能，妥善保管API密钥
2. **日志权限**: 设置适当的日志文件权限
3. **系统监控**: 监控系统资源使用情况
4. **定期更新**: 保持依赖包和系统的更新

## 📞 技术支持

如遇到问题，请检查：

1. 系统日志文件
2. 网络连接状态
3. 模型文件完整性
4. 依赖包版本兼容性

---

**注意**: 本系统仅用于学习和研究目的，不构成投资建议。实际交易请谨慎决策。
