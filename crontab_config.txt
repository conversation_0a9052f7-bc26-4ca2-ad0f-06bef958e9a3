# BTC价格预测系统 - 优化版定时任务配置
#
# 系统架构说明：
# 1. real_time_predict.py - 实时预测脚本，每30分钟执行
# 2. daily_reflection.py - 反思学习脚本，每天凌晨执行
# 3. monitor_system.py - 系统监控脚本，每小时执行
#
# 注意：请根据实际Python环境路径修改python路径

# 每30分钟进行一次BTC价格预测
*/30 * * * * cd /ai-model && python real_time_predict.py >> logs/predict.log 2>&1

# 每天凌晨2点进行反思学习（分析预测结果，优化模型）
0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控（检查系统状态和性能）
0 * * * * cd /ai-model && python monitor_system.py >> logs/monitor.log 2>&1

# 每周日凌晨3点清理旧日志文件（可选）
0 3 * * 0 cd /ai-model && find logs/ -name "*.log" -mtime +30 -delete
