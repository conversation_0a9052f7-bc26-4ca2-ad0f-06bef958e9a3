# BTC价格预测系统 - 最终优化版使用指南

## 🎯 系统概述

这是一个完全自动化的BTC价格预测系统，具备持续学习能力。系统会每30分钟自动预测BTC价格涨跌，每天自动分析预测结果并从错误中学习，不断提高预测准确率。

## 🔧 核心组件

### 1. 训练组件
- **train_initial_model.py** - 初始模型训练脚本

### 2. 预测组件
- **continuous_predict.py** - 持续运行的预测脚本（推荐）
- **real_time_predict.py** - 单次预测脚本

### 3. 学习组件
- **daily_reflection.py** - 反思学习脚本

### 4. 监控组件
- **monitor_system.py** - 系统监控脚本

### 5. 部署组件
- **setup_optimized_system.py** - 系统部署测试脚本

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install pandas numpy lightgbm scikit-learn requests joblib ta matplotlib seaborn

# 检查系统
python setup_optimized_system.py
```

### 2. 训练初始模型
```bash
python train_initial_model.py
```

### 3. 启动预测系统
```bash
# 方式1：持续运行（推荐）
python continuous_predict.py

# 方式2：定时任务
# 在crontab中添加：
# */30 * * * * cd /ai-model && python real_time_predict.py >> logs/predict.log 2>&1
```

### 4. 设置反思学习
```bash
# 在crontab中添加：
# 0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1
```

## 🔄 系统工作流程

### 预测流程（每30分钟）
1. **数据获取**: 从币安API获取最新BTC/USDT K线数据
2. **特征工程**: 计算技术指标（RSI、MACD、布林带、ADX等）
3. **模型预测**: 使用训练好的模型预测价格涨跌
4. **结果记录**: 保存预测结果到 `prediction_log.csv`

### 反思学习流程（每日）
1. **数据验证**: 获取历史数据，验证之前的预测结果
2. **准确率计算**: 使用与训练时相同的动态阈值计算准确率
3. **错误分析**: 识别预测错误的样本
4. **学习决策**: 如果准确率低于55%且错误样本≥5个，触发重训练
5. **增量学习**: 使用错误样本进行增量训练，更新模型
6. **日志清理**: 清理预测日志，为下一轮做准备

## 📊 关键特性

### 智能阈值计算
- 使用与训练时完全相同的动态阈值逻辑
- 自动适应不同数据量的情况
- 确保预测验证的一致性

### 持续运行模式
- `continuous_predict.py` 可以持续运行，每30分钟自动预测
- 优雅停止机制（Ctrl+C）
- 完善的错误处理和重试机制

### 增量学习
- 从预测错误中学习，不断改进模型
- 使用样本权重，让模型更关注错误样本
- 保存模型版本历史

## 📁 文件结构

```
/ai-model/
├── train_initial_model.py          # 初始模型训练
├── continuous_predict.py           # 持续预测脚本（推荐）
├── real_time_predict.py            # 单次预测脚本
├── daily_reflection.py             # 反思学习脚本
├── monitor_system.py               # 系统监控脚本
├── setup_optimized_system.py       # 部署测试脚本
├── FINAL_SYSTEM_GUIDE.md           # 最终使用指南
├── btc_reflection_model_v1/         # 模型文件目录
│   ├── lgbm_model.joblib           # 当前模型
│   ├── lgbm_model_iter_*.joblib    # 历史模型版本
│   ├── scaler.joblib               # 特征缩放器
│   ├── feature_columns.joblib      # 特征列名
│   ├── prediction_log.csv          # 预测日志
│   └── reflection_data.csv         # 反思数据存档
└── logs/                           # 日志目录
    ├── predict.log                 # 预测日志
    ├── reflection.log              # 反思学习日志
    └── monitor.log                 # 监控日志
```

## ⚙️ 配置参数

### 预测配置
- `PREDICTION_INTERVAL`: 预测间隔（默认30分钟）
- `SYMBOL`: 交易对（默认BTCUSDT）
- `INTERVAL`: K线间隔（默认30m）

### 反思学习配置
- `MIN_PREDICTION_SAMPLES`: 最少预测样本数（默认24条）
- `MIN_ERROR_SAMPLES`: 最少错误样本数（默认5条）
- `ACCURACY_THRESHOLD`: 触发重训练的准确率阈值（默认55%）

## 🔍 使用方法

### 启动持续预测
```bash
# 前台运行（测试用）
python continuous_predict.py

# 后台运行（生产环境）
nohup python continuous_predict.py > logs/continuous_predict.log 2>&1 &
```

### 手动执行反思学习
```bash
python daily_reflection.py
```

### 查看系统状态
```bash
# 查看预测日志
tail -f logs/predict.log

# 查看反思学习日志
tail -f logs/reflection.log

# 查看监控报告
python monitor_system.py
```

### 停止系统
```bash
# 如果是前台运行，按 Ctrl+C
# 如果是后台运行，找到进程ID并终止
ps aux | grep continuous_predict.py
kill <PID>
```

## 📈 监控和维护

### 检查预测活动
```bash
# 查看预测日志文件
ls -la btc_reflection_model_v1/prediction_log.csv

# 查看最近的预测
tail btc_reflection_model_v1/prediction_log.csv
```

### 检查学习效果
```bash
# 查看模型版本
ls -la btc_reflection_model_v1/lgbm_model_iter_*.joblib

# 查看反思数据
tail btc_reflection_model_v1/reflection_data.csv
```

### 性能监控
```bash
# 运行系统监控
python monitor_system.py

# 查看监控报告
cat btc_reflection_model_v1/system_monitor_report.json
```

## 🔧 故障排除

### 常见问题

1. **预测脚本无法启动**
   - 检查模型文件是否存在
   - 确认网络连接正常
   - 验证Python环境和依赖

2. **准确率异常**
   - 检查动态阈值计算
   - 验证历史数据质量
   - 确认时间戳对齐

3. **反思学习不触发**
   - 确认预测样本数量≥24
   - 检查准确率是否低于阈值
   - 验证错误样本数量≥5

### 调试技巧

```bash
# 测试单次预测
python real_time_predict.py

# 测试反思学习
python daily_reflection.py

# 检查系统状态
python setup_optimized_system.py
```

## 🎯 最佳实践

1. **生产环境部署**
   - 使用 `continuous_predict.py` 持续运行
   - 设置日志轮转避免文件过大
   - 定期备份模型文件

2. **监控建议**
   - 每小时运行监控脚本
   - 设置准确率告警
   - 监控预测频率

3. **维护建议**
   - 定期清理旧日志
   - 备份重要模型版本
   - 监控系统资源使用

## 🚀 系统优势

- ✅ **完全自动化**: 无需人工干预，自动预测和学习
- ✅ **持续改进**: 从错误中学习，不断提高准确率
- ✅ **实时监控**: 全面的系统状态监控
- ✅ **容错机制**: 完善的错误处理和恢复
- ✅ **易于部署**: 简单的配置和启动流程

---

**注意**: 本系统仅用于学习和研究目的，不构成投资建议。加密货币投资存在高风险，请谨慎决策。
