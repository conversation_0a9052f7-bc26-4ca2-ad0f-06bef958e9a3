# BTC价格预测系统 - 简化版定时任务配置
# 复制以下内容到crontab中

# 每天凌晨2点进行反思学习
0 2 * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控
0 * * * * cd /ai-model && /home/<USER>/miniconda3/envs/ml_env/bin/python monitor_system.py >> logs/monitor.log 2>&1

# 系统重启时自动启动持续预测脚本
@reboot cd /ai-model && nohup /home/<USER>/miniconda3/envs/ml_env/bin/python continuous_predict.py > logs/continuous_predict.log 2>&1 &
