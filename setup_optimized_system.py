#!/usr/bin/env python3
"""
BTC价格预测系统 - 优化版部署脚本
功能：
1. 检查系统环境和依赖
2. 验证模型文件完整性
3. 测试各个组件功能
4. 设置定时任务
5. 生成部署报告

使用方法：
python setup_optimized_system.py
"""

import os
import sys
import subprocess
import importlib
import requests
import joblib
from datetime import datetime
import json

# --- 配置参数 ---
MODEL_DIR = "btc_reflection_model_v1"
LOGS_DIR = "logs"
REQUIRED_PACKAGES = [
    'pandas', 'numpy', 'lightgbm', 'scikit-learn', 
    'requests', 'joblib', 'ta', 'matplotlib', 'seaborn'
]

def print_header(title):
    """打印格式化的标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step_name):
    """打印步骤名称"""
    print(f"\n--- {step_name} ---")

def check_python_version():
    """检查Python版本"""
    print_step("检查Python版本")
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，建议使用Python 3.7+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查依赖包"""
    print_step("检查依赖包")
    
    missing_packages = []
    
    for package in REQUIRED_PACKAGES:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    else:
        print("\n✅ 所有依赖包已安装")
        return True

def check_model_files():
    """检查模型文件"""
    print_step("检查模型文件")
    
    required_files = [
        "lgbm_model.joblib",
        "scaler.joblib", 
        "feature_columns.joblib"
    ]
    
    all_exist = True
    
    if not os.path.exists(MODEL_DIR):
        print(f"❌ 模型目录不存在: {MODEL_DIR}")
        return False
    
    for file in required_files:
        file_path = os.path.join(MODEL_DIR, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file} ({size} bytes)")
        else:
            print(f"❌ {file} - 文件不存在")
            all_exist = False
    
    if not all_exist:
        print("\n请先运行 train_initial_model.py 训练初始模型")
        return False
    
    return True

def test_model_loading():
    """测试模型加载"""
    print_step("测试模型加载")
    
    try:
        model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
        scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
        feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))
        
        print(f"✅ 模型加载成功")
        print(f"   特征数量: {len(feature_columns)}")
        print(f"   模型类型: {type(model).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_api_connection():
    """测试币安API连接"""
    print_step("测试币安API连接")
    
    try:
        url = "https://api.binance.com/api/v3/ping"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        print("✅ 币安API连接正常")
        
        # 测试获取K线数据
        klines_url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': 'BTCUSDT',
            'interval': '30m',
            'limit': 5
        }
        
        response = requests.get(klines_url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        print(f"✅ K线数据获取正常 (获取到{len(data)}条数据)")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print_step("创建目录结构")
    
    directories = [LOGS_DIR, MODEL_DIR]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录已存在: {directory}")
    
    return True

def test_prediction_script():
    """测试预测脚本"""
    print_step("测试预测脚本")
    
    try:
        # 检查脚本文件是否存在
        if not os.path.exists("real_time_predict.py"):
            print("❌ real_time_predict.py 文件不存在")
            return False
        
        print("✅ 预测脚本文件存在")
        
        # 可以添加更多测试逻辑，比如语法检查等
        return True
        
    except Exception as e:
        print(f"❌ 预测脚本测试失败: {e}")
        return False

def test_reflection_script():
    """测试反思脚本"""
    print_step("测试反思脚本")
    
    try:
        # 检查脚本文件是否存在
        if not os.path.exists("daily_reflection.py"):
            print("❌ daily_reflection.py 文件不存在")
            return False
        
        print("✅ 反思脚本文件存在")
        return True
        
    except Exception as e:
        print(f"❌ 反思脚本测试失败: {e}")
        return False

def test_monitor_script():
    """测试监控脚本"""
    print_step("测试监控脚本")
    
    try:
        # 检查脚本文件是否存在
        if not os.path.exists("monitor_system.py"):
            print("❌ monitor_system.py 文件不存在")
            return False
        
        print("✅ 监控脚本文件存在")
        return True
        
    except Exception as e:
        print(f"❌ 监控脚本测试失败: {e}")
        return False

def generate_crontab_config():
    """生成crontab配置"""
    print_step("生成定时任务配置")
    
    current_dir = os.getcwd()
    python_path = sys.executable
    
    crontab_content = f"""# BTC价格预测系统 - 优化版定时任务配置
# 生成时间: {datetime.now()}
# 工作目录: {current_dir}
# Python路径: {python_path}

# 每30分钟进行一次BTC价格预测
*/30 * * * * cd {current_dir} && {python_path} real_time_predict.py >> logs/predict.log 2>&1

# 每天凌晨2点进行反思学习
0 2 * * * cd {current_dir} && {python_path} daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控
0 * * * * cd {current_dir} && {python_path} monitor_system.py >> logs/monitor.log 2>&1

# 每周日凌晨3点清理旧日志文件
0 3 * * 0 cd {current_dir} && find logs/ -name "*.log" -mtime +30 -delete
"""
    
    # 保存配置文件
    with open("crontab_optimized.txt", "w") as f:
        f.write(crontab_content)
    
    print("✅ 定时任务配置已生成: crontab_optimized.txt")
    print("\n要设置定时任务，请执行:")
    print("1. crontab -e")
    print("2. 将 crontab_optimized.txt 的内容添加到crontab中")
    print("3. 保存并退出")
    
    return True

def generate_deployment_report(test_results):
    """生成部署报告"""
    print_step("生成部署报告")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'system_info': {
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'working_directory': os.getcwd(),
            'python_executable': sys.executable
        },
        'test_results': test_results,
        'overall_status': 'success' if all(test_results.values()) else 'failed',
        'next_steps': []
    }
    
    # 添加后续步骤建议
    if report['overall_status'] == 'success':
        report['next_steps'] = [
            "设置crontab定时任务",
            "监控系统运行状态",
            "定期检查预测准确率",
            "根据需要调整模型参数"
        ]
    else:
        failed_tests = [test for test, result in test_results.items() if not result]
        report['next_steps'] = [f"修复失败的测试: {', '.join(failed_tests)}"]
    
    # 保存报告
    with open("deployment_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("✅ 部署报告已生成: deployment_report.json")
    return True

def main():
    """主函数"""
    print_header("BTC价格预测系统 - 优化版部署检查")
    print(f"开始时间: {datetime.now()}")
    print(f"工作目录: {os.getcwd()}")
    
    # 执行所有检查
    test_results = {}
    
    test_functions = [
        ("Python版本检查", check_python_version),
        ("依赖包检查", check_dependencies),
        ("创建目录", create_directories),
        ("模型文件检查", check_model_files),
        ("模型加载测试", test_model_loading),
        ("API连接测试", test_api_connection),
        ("预测脚本测试", test_prediction_script),
        ("反思脚本测试", test_reflection_script),
        ("监控脚本测试", test_monitor_script),
        ("生成定时任务配置", generate_crontab_config)
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            test_results[test_name] = False
    
    # 生成部署报告
    generate_deployment_report(test_results)
    
    # 显示总结
    print_header("部署检查总结")
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有检查通过！系统已准备好部署")
        print("\n下一步:")
        print("1. 设置定时任务 (参考 crontab_optimized.txt)")
        print("2. 监控系统运行状态")
        print("3. 定期检查预测准确率")
    else:
        print("❌ 部分检查失败，请修复后重新运行")
        failed_tests = [test for test, result in test_results.items() if not result]
        print(f"失败的测试: {', '.join(failed_tests)}")
    
    print(f"\n完成时间: {datetime.now()}")

if __name__ == "__main__":
    main()
