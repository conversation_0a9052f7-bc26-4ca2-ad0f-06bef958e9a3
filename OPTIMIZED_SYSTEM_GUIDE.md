# BTC价格预测系统 - 优化版使用指南

## 📋 系统概述

这是一个基于机器学习的BTC价格预测系统，具备自动反思学习能力。系统经过优化，采用模块化设计，包含以下核心组件：

### 🔧 核心组件

1. **train_initial_model.py** - 初始模型训练脚本
2. **real_time_predict.py** - 实时预测脚本（每30分钟执行）
3. **daily_reflection.py** - 反思学习脚本（每日执行）
4. **monitor_system.py** - 系统监控脚本（每小时执行）
5. **setup_optimized_system.py** - 系统部署和测试脚本

### 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   币安API       │    │   实时预测       │    │   预测日志       │
│   (K线数据)     │───▶│   (30分钟)      │───▶│   (CSV文件)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   更新模型       │◀───│   反思学习       │◀───│   历史数据       │
│   (增量训练)     │    │   (每日)        │    │   (验证结果)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   系统监控       │
                       │   (每小时)      │
                       └─────────────────┘
```

## 🚀 快速开始

### 1. 环境准备

确保您的系统满足以下要求：
- Python 3.7+
- 网络连接（访问币安API）
- 足够的磁盘空间（存储模型和日志）

### 2. 安装依赖

```bash
pip install pandas numpy lightgbm scikit-learn requests joblib ta matplotlib seaborn
```

### 3. 训练初始模型

```bash
python train_initial_model.py
```

这将创建 `btc_reflection_model_v1/` 目录并生成以下文件：
- `lgbm_model.joblib` - 训练好的模型
- `scaler.joblib` - 特征缩放器
- `feature_columns.joblib` - 特征列名

### 4. 系统部署检查

```bash
python setup_optimized_system.py
```

这将执行完整的系统检查，包括：
- Python版本和依赖包检查
- 模型文件完整性验证
- API连接测试
- 脚本功能测试
- 生成定时任务配置

### 5. 设置定时任务

```bash
crontab -e
```

将 `crontab_optimized.txt` 的内容添加到crontab中：

```bash
# 每30分钟进行一次BTC价格预测
*/30 * * * * cd /ai-model && python real_time_predict.py >> logs/predict.log 2>&1

# 每天凌晨2点进行反思学习
0 2 * * * cd /ai-model && python daily_reflection.py >> logs/reflection.log 2>&1

# 每小时进行系统监控
0 * * * * cd /ai-model && python monitor_system.py >> logs/monitor.log 2>&1
```

## 📊 系统工作流程

### 实时预测流程（每30分钟）

1. **数据获取**: 从币安API获取最新的BTC/USDT 30分钟K线数据
2. **特征工程**: 计算技术指标（RSI、MACD、布林带等）
3. **模型预测**: 使用训练好的模型预测价格涨跌
4. **结果记录**: 将预测结果保存到 `prediction_log.csv`

### 反思学习流程（每日凌晨2点）

1. **数据验证**: 获取历史数据，验证之前的预测结果
2. **准确率评估**: 计算预测准确率，识别错误样本
3. **学习决策**: 如果准确率低于阈值且错误样本足够，触发重训练
4. **增量学习**: 使用错误样本进行增量训练，更新模型
5. **日志清理**: 清理预测日志，为下一轮做准备

### 系统监控流程（每小时）

1. **文件检查**: 检查模型文件和日志文件状态
2. **活动分析**: 分析预测活动频率和最近表现
3. **准确率监控**: 监控预测准确率趋势
4. **状态报告**: 生成系统健康报告

## 📁 文件结构

```
/ai-model/
├── train_initial_model.py          # 初始模型训练
├── real_time_predict.py            # 实时预测脚本
├── daily_reflection.py             # 反思学习脚本
├── monitor_system.py               # 系统监控脚本
├── setup_optimized_system.py       # 部署测试脚本
├── OPTIMIZED_SYSTEM_GUIDE.md       # 系统使用指南
├── crontab_optimized.txt           # 定时任务配置
├── btc_reflection_model_v1/         # 模型文件目录
│   ├── lgbm_model.joblib           # 训练好的模型
│   ├── scaler.joblib               # 特征缩放器
│   ├── feature_columns.joblib      # 特征列名
│   ├── prediction_log.csv          # 预测日志
│   ├── reflection_data.csv         # 反思数据存档
│   └── system_monitor_report.json  # 监控报告
└── logs/                           # 日志目录
    ├── predict.log                 # 预测日志
    ├── reflection.log              # 反思学习日志
    └── monitor.log                 # 监控日志
```

## 🔍 监控和维护

### 查看系统状态

```bash
# 查看最新监控报告
cat btc_reflection_model_v1/system_monitor_report.json

# 实时查看预测日志
tail -f logs/predict.log

# 实时查看反思学习日志
tail -f logs/reflection.log

# 实时查看监控日志
tail -f logs/monitor.log
```

### 手动执行组件

```bash
# 手动执行预测
python real_time_predict.py

# 手动执行反思学习
python daily_reflection.py

# 手动执行系统监控
python monitor_system.py
```

### 检查预测准确率

```bash
# 查看最近的反思学习结果
grep "准确率" logs/reflection.log | tail -5

# 查看模型更新记录
ls -la btc_reflection_model_v1/lgbm_model_iter_*.joblib
```

## ⚙️ 配置参数

### 实时预测配置 (real_time_predict.py)

- `SYMBOL`: 交易对（默认：BTCUSDT）
- `INTERVAL`: K线间隔（默认：30m）
- `KLINES_LIMIT`: 获取的历史K线数量（默认：150）

### 反思学习配置 (daily_reflection.py)

- `MIN_PREDICTION_SAMPLES`: 最少预测样本数（默认：24）
- `MIN_ERROR_SAMPLES`: 最少错误样本数（默认：5）
- `ACCURACY_THRESHOLD`: 触发重训练的准确率阈值（默认：55%）
- `DAYS_BACK`: 获取历史数据的天数（默认：7天）

### 监控配置 (monitor_system.py)

- `MIN_PREDICTION_FREQUENCY`: 24小时内最少预测次数（默认：24）
- `ACCURACY_WARNING_THRESHOLD`: 准确率警告阈值（默认：50%）
- `ACCURACY_CRITICAL_THRESHOLD`: 准确率严重警告阈值（默认：40%）

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 确认币安API可访问
   - 检查防火墙设置

2. **模型加载失败**
   - 确认模型文件存在且完整
   - 重新训练初始模型
   - 检查文件权限

3. **预测结果异常**
   - 检查输入数据质量
   - 验证特征工程逻辑
   - 查看错误日志

4. **定时任务不执行**
   - 检查crontab配置
   - 确认Python路径正确
   - 检查工作目录权限

### 性能优化建议

1. **定期清理日志**: 避免日志文件过大影响性能
2. **监控磁盘空间**: 确保有足够空间存储模型和日志
3. **调整预测频率**: 根据需要调整预测间隔
4. **优化模型参数**: 根据实际表现调整学习参数

## 📈 系统优势

1. **模块化设计**: 各组件职责明确，易于维护和扩展
2. **自动化运行**: 完全自动化的预测和学习流程
3. **实时监控**: 全面的系统状态监控和报告
4. **增量学习**: 持续从错误中学习，不断改进模型
5. **容错机制**: 完善的错误处理和恢复机制

## 🎯 下一步计划

1. 添加更多技术指标和特征
2. 实现多时间框架预测
3. 添加风险管理功能
4. 集成更多数据源
5. 开发Web界面监控面板

---

**注意**: 本系统仅用于学习和研究目的，不构成投资建议。加密货币投资存在高风险，请谨慎决策。
