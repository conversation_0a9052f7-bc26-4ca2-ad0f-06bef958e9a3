#!/usr/bin/env python3
"""
测试预测验证功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from continuous_predict import *
import warnings
warnings.filterwarnings('ignore')

def create_test_prediction_log():
    """创建测试用的预测日志"""
    # 清理旧文件
    if os.path.exists(PREDICTION_LOG_PATH):
        os.remove(PREDICTION_LOG_PATH)
    
    # 创建测试数据
    base_time = datetime(2025, 7, 14, 12, 0, 0)
    test_data = []
    
    for i in range(3):
        timestamp = base_time + timedelta(minutes=30 * i)
        price = 121000 + i * 100  # 模拟价格变化
        
        log_data = {
            'timestamp_utc': timestamp,
            'close_price': price,
            'prediction': 1,  # 都预测上涨
            'prediction_proba_down': 0.3,
            'prediction_proba_up': 0.7,
            'prediction_confidence': 0.7,
            'actual_direction': np.nan,
            'is_correct': np.nan,
            'price_change': np.nan
        }
        test_data.append(log_data)
    
    # 保存测试数据
    df = pd.DataFrame(test_data)
    df.set_index('timestamp_utc', inplace=True)
    df.to_csv(PREDICTION_LOG_PATH)
    
    print("✅ 创建测试预测日志成功")
    print(df)
    return df

def test_verification_function():
    """测试验证函数"""
    print("🧪 测试预测验证功能")
    
    # 创建测试数据
    test_df = create_test_prediction_log()
    
    # 测试验证第二个预测（使用第三个时间点的价格）
    current_time = datetime(2025, 7, 14, 13, 0, 0)  # 第三个时间点
    current_price = 121250  # 比第二个预测的价格(121100)高，应该是上涨
    
    print(f"\n🔍 测试验证功能:")
    print(f"   当前时间: {current_time}")
    print(f"   当前价格: ${current_price}")
    print(f"   查找30分钟前的预测...")
    
    verification_result = verify_previous_prediction(current_price, current_time)
    
    if verification_result:
        print(f"✅ 验证成功!")
        print(f"   预测时间: {verification_result['prediction_time']}")
        print(f"   预测价格: ${verification_result['previous_price']}")
        print(f"   当前价格: ${verification_result['current_price']}")
        print(f"   价格变化: {verification_result['price_change']:+.3%}")
        print(f"   预测方向: {'上涨' if verification_result['predicted_direction'] == 1 else '下跌'}")
        print(f"   实际方向: {'上涨' if verification_result['actual_direction'] == 1 else '下跌'}")
        print(f"   预测正确: {'✅' if verification_result['is_correct'] else '❌'}")
        
        # 更新预测日志
        update_prediction_accuracy(verification_result)
        print(f"✅ 预测日志已更新")
        
        # 显示更新后的日志
        print(f"\n📋 更新后的预测日志:")
        df_updated = pd.read_csv(PREDICTION_LOG_PATH)
        print(df_updated)
        
        # 计算准确率
        accuracy_stats = calculate_current_accuracy()
        if accuracy_stats:
            print(f"\n📊 准确率统计:")
            print(f"   总预测次数: {accuracy_stats['total_predictions']}")
            print(f"   已验证预测: {accuracy_stats['verified_predictions']}")
            print(f"   正确预测次数: {accuracy_stats['correct_predictions']}")
            print(f"   整体准确率: {accuracy_stats['overall_accuracy']:.2%}")
        
    else:
        print("❌ 验证失败")

def test_continuous_prediction_simulation():
    """测试持续预测模拟"""
    print("\n🚀 测试持续预测模拟")
    
    # 加载模型
    if not load_model_and_preprocessors():
        print("❌ 模型加载失败")
        return
    
    # 清理旧日志
    if os.path.exists(PREDICTION_LOG_PATH):
        os.remove(PREDICTION_LOG_PATH)
    
    # 模拟3次预测
    base_time = datetime(2025, 7, 14, 12, 0, 0)
    
    for i in range(3):
        print(f"\n--- 第 {i+1} 次预测 ---")
        
        # 获取真实数据
        klines_data = fetch_latest_klines_from_binance()
        if klines_data is None:
            continue
        
        # 修改时间戳模拟不同时间点
        new_timestamp = base_time + timedelta(minutes=30 * i)
        klines_data.index = klines_data.index[:-1].tolist() + [new_timestamp]
        
        # 稍微调整价格
        price_change = 0.001 * (i - 1)  # -0.1%, 0%, +0.1%
        klines_data.loc[new_timestamp, 'close'] *= (1 + price_change)
        
        print(f"模拟时间: {new_timestamp}")
        current_price = klines_data.loc[new_timestamp, 'close'].iloc[0] if hasattr(klines_data.loc[new_timestamp, 'close'], 'iloc') else klines_data.loc[new_timestamp, 'close']
        print(f"模拟价格: ${float(current_price):.2f}")
        
        # 如果不是第一次预测，先验证上次预测
        if i > 0:
            current_price_val = klines_data.loc[new_timestamp, 'close'].iloc[0] if hasattr(klines_data.loc[new_timestamp, 'close'], 'iloc') else klines_data.loc[new_timestamp, 'close']
            verification_result = verify_previous_prediction(current_price_val, new_timestamp)
            
            if verification_result:
                print(f"🔍 验证上次预测: {'✅ 正确' if verification_result['is_correct'] else '❌ 错误'}")
                update_prediction_accuracy(verification_result)
            else:
                print(f"🔍 无法验证上次预测")
        
        # 进行新预测
        prediction_result = make_prediction(klines_data)
        if prediction_result:
            print(f"📈 新预测: {'🔺 上涨' if prediction_result['prediction'] == 1 else '🔻 下跌'}")
            print(f"   置信度: {prediction_result['prediction_confidence']:.2%}")
            save_prediction_log(prediction_result)
        
        # 显示当前准确率
        accuracy_stats = calculate_current_accuracy()
        if accuracy_stats and accuracy_stats['verified_predictions'] > 0:
            print(f"📊 当前准确率: {accuracy_stats['overall_accuracy']:.2%} ({accuracy_stats['correct_predictions']}/{accuracy_stats['verified_predictions']})")
    
    print(f"\n✅ 模拟完成")
    
    # 显示最终结果
    if os.path.exists(PREDICTION_LOG_PATH):
        print(f"\n📋 最终预测日志:")
        df = pd.read_csv(PREDICTION_LOG_PATH)
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
        
        for _, row in df.iterrows():
            timestamp = row['timestamp_utc'].strftime('%H:%M')
            pred_dir = "上涨" if row['prediction'] == 1 else "下跌"
            
            if pd.notna(row['actual_direction']):
                actual_dir = "上涨" if row['actual_direction'] == 1 else "下跌"
                result = "✅" if row['is_correct'] else "❌"
                print(f"   {timestamp} | 预测:{pred_dir} | 实际:{actual_dir} | {result}")
            else:
                print(f"   {timestamp} | 预测:{pred_dir} | 实际:待验证 | ⏳")

def main():
    """主函数"""
    print("BTC预测验证功能测试")
    print("1. 测试验证函数")
    print("2. 测试持续预测模拟")
    
    choice = input("\n请选择测试 (1/2): ").strip()
    
    if choice == "1":
        test_verification_function()
    elif choice == "2":
        test_continuous_prediction_simulation()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
