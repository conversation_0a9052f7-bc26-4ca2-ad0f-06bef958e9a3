#!/usr/bin/env python3
"""
模型诊断脚本
用于分析模型为什么总是预测上涨
"""

import pandas as pd
import numpy as np
import joblib
import os
from test_prediction_fixed import *
import warnings
warnings.filterwarnings('ignore')

def diagnose_model():
    """诊断模型问题"""
    print("🔍 开始模型诊断...")
    
    # 1. 加载模型
    model, scaler, feature_columns = load_model_and_preprocessors()
    if model is None:
        return
    
    # 2. 获取数据
    klines_data = fetch_latest_klines_from_binance()
    if klines_data is None:
        return
    
    df_featured = create_features(klines_data)
    if df_featured.empty:
        print("❌ 特征工程失败")
        return
    
    print(f"✅ 获取到 {len(df_featured)} 条特征数据")
    
    # 3. 测试不同时间点的预测
    print("\n📊 测试不同时间点的预测:")
    test_count = min(10, len(df_featured))
    
    predictions = []
    for i in range(test_count):
        idx = -(i+1)  # 从最新开始往前
        test_data = df_featured.iloc[idx:idx+1].copy()
        
        if len(test_data) == 0:
            continue
            
        try:
            X_test = test_data[feature_columns]
            X_test_scaled = scaler.transform(X_test)
            
            prediction = model.predict(X_test_scaled)[0]
            prediction_proba = model.predict_proba(X_test_scaled)[0]
            
            result = {
                'timestamp': test_data.index[0],
                'close': test_data['close'].iloc[0],
                'prediction': prediction,
                'proba_down': prediction_proba[0],
                'proba_up': prediction_proba[1]
            }
            predictions.append(result)
            
            print(f"   {result['timestamp']}: 价格=${result['close']:.2f}, 预测={result['prediction']}, 上涨概率={result['proba_up']:.4f}")
            
        except Exception as e:
            print(f"   预测失败: {e}")
    
    # 4. 分析预测结果
    if predictions:
        df_pred = pd.DataFrame(predictions)
        print(f"\n📈 预测结果统计:")
        print(f"   上涨预测次数: {(df_pred['prediction'] == 1).sum()}")
        print(f"   下跌预测次数: {(df_pred['prediction'] == 0).sum()}")
        print(f"   上涨概率范围: [{df_pred['proba_up'].min():.4f}, {df_pred['proba_up'].max():.4f}]")
        print(f"   上涨概率平均: {df_pred['proba_up'].mean():.4f}")
        
        # 检查是否所有预测都相同
        unique_predictions = df_pred['prediction'].nunique()
        unique_probabilities = df_pred['proba_up'].nunique()
        
        if unique_predictions == 1:
            print("⚠️ 警告：所有预测结果都相同！")
        
        if unique_probabilities == 1:
            print("⚠️ 警告：所有预测概率都相同！")
    
    # 5. 检查特征值分布
    print(f"\n🔍 检查特征值分布:")
    latest_features = df_featured.iloc[-1][feature_columns]
    
    print(f"   特征值统计:")
    print(f"   - 最小值: {latest_features.min():.6f}")
    print(f"   - 最大值: {latest_features.max():.6f}")
    print(f"   - 平均值: {latest_features.mean():.6f}")
    print(f"   - 标准差: {latest_features.std():.6f}")
    
    # 检查是否有异常值
    inf_count = np.isinf(latest_features).sum()
    nan_count = np.isnan(latest_features).sum()
    
    if inf_count > 0:
        print(f"⚠️ 警告：发现 {inf_count} 个无穷大值")
    if nan_count > 0:
        print(f"⚠️ 警告：发现 {nan_count} 个NaN值")
    
    # 6. 检查缩放后的特征值
    print(f"\n🔍 检查缩放后的特征值:")
    X_latest = latest_features.values.reshape(1, -1)
    X_scaled = scaler.transform(X_latest)
    
    print(f"   缩放后特征值统计:")
    print(f"   - 最小值: {X_scaled.min():.6f}")
    print(f"   - 最大值: {X_scaled.max():.6f}")
    print(f"   - 平均值: {X_scaled.mean():.6f}")
    print(f"   - 标准差: {X_scaled.std():.6f}")
    
    # 7. 测试人工构造的不同特征值
    print(f"\n🧪 测试人工构造的特征值:")
    
    # 创建一些变化的测试数据
    base_features = latest_features.values.copy()
    
    test_cases = [
        ("原始数据", base_features),
        ("所有特征*0.5", base_features * 0.5),
        ("所有特征*2.0", base_features * 2.0),
        ("随机噪声", base_features + np.random.normal(0, 0.1, len(base_features))),
        ("负值测试", -np.abs(base_features))
    ]
    
    for name, test_features in test_cases:
        try:
            X_test = test_features.reshape(1, -1)
            X_test_scaled = scaler.transform(X_test)
            
            prediction = model.predict(X_test_scaled)[0]
            prediction_proba = model.predict_proba(X_test_scaled)[0]
            
            print(f"   {name}: 预测={prediction}, 上涨概率={prediction_proba[1]:.4f}")
            
        except Exception as e:
            print(f"   {name}: 测试失败 - {e}")
    
    # 8. 检查模型特征重要性
    print(f"\n📊 模型特征重要性 (前10个):")
    if hasattr(model, 'feature_importances_'):
        importance_df = pd.DataFrame({
            'feature': feature_columns,
            'importance': model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        for i, row in importance_df.head(10).iterrows():
            print(f"   {row['feature']}: {row['importance']:.6f}")
    
    print("\n✅ 诊断完成")

if __name__ == "__main__":
    diagnose_model()
